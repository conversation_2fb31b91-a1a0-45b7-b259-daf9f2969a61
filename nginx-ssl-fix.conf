# 专门解决 SSL_ERROR_NO_CYPHER_OVERLAP 问题的 Nginx 配置
# 这个配置重点关注 SSL/TLS 加密套件的兼容性

server {
    listen 443 ssl http2;
    server_name trip.guijimada.cn;
    
    # SSL 证书路径（请替换为您的实际证书路径）
    ssl_certificate /etc/ssl/certs/your-certificate.crt;
    ssl_certificate_key /etc/ssl/private/your-private-key.key;
    
    # === 解决 SSL_ERROR_NO_CYPHER_OVERLAP 的关键配置 ===
    
    # 1. SSL 协议版本 - 支持更广泛的客户端
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # 2. 加密套件配置 - 包含更多兼容的加密算法
    # 这个配置包含了现代浏览器和旧版浏览器都支持的加密套件
    ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-SHA384:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA:ECDHE-RSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES128-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA';
    
    # 3. 让客户端选择加密套件（提高兼容性）
    ssl_prefer_server_ciphers off;
    
    # 4. SSL 会话配置
    ssl_session_cache shared:SSL:50m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # 5. DH 参数（如果使用 DHE 加密套件）
    # ssl_dhparam /etc/ssl/certs/dhparam.pem;
    
    # === 其他 SSL 安全配置 ===
    
    # HSTS（HTTP Strict Transport Security）
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    
    # 其他安全头
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # === 应用程序配置 ===
    
    # 前端静态文件
    location / {
        root /var/www/frugal-voyager-client/dist;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # API 代理到后端
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 日志
    access_log /var/log/nginx/ssl-access.log;
    error_log /var/log/nginx/ssl-error.log;
}

# HTTP 重定向到 HTTPS
server {
    listen 80;
    server_name trip.guijimada.cn;
    return 301 https://$server_name$request_uri;
}
