import { ref, reactive } from "vue";
import { travelGuideService } from "@/api/services/travel-guide-service";

// 类型定义
export interface TimeData {
  [dayIndex: number]: string[];
}

/**
 * 行程项目类型
 */
export type ItineraryItemType = "attraction" | "transportation";

/**
 * 行程项目基础接口
 */
export interface ItineraryItemBase {
  id: string;
  type: ItineraryItemType;
  time: string;
}

/**
 * 景点项目接口
 */
export interface AttractionItem extends ItineraryItemBase {
  type: "attraction";
  name: string;
  description: string;
  ticketPrice: string;
  recommendedStay: string;
  imageUrl: string;
  position: "left" | "right";
}

/**
 * 交通方式项目接口
 */
export interface TransportationItem extends ItineraryItemBase {
  type: "transportation";
  icon: string;
  duration: string;
  detail: string;
}

/**
 * 行程项目类型（联合类型）
 */
export type ItineraryItem = AttractionItem | TransportationItem;

/**
 * 行程日期数据接口
 */
export interface DayItem {
  date: string;
  number: number;
  label: string;
}

/**
 * 行程数据
 * 可以从API获取，这里提供默认数据
 */
export const itineraryItems = reactive<ItineraryItem[]>([
  // 可以是交通方式开始
  {
    id: "transport-initial",
    type: "transportation",
    time: "07:00 - 08:00",
    icon: "✈️",
    duration: "1小时",
    detail: "从北京首都机场飞往上海虹桥机场",
  },
  // 然后是景点
  {
    id: "shanghai-hotel",
    type: "attraction",
    time: "09:00 - 10:30",
    name: "上海迎宾馆",
    description:
      "上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。",
    ticketPrice: "免费",
    recommendedStay: "1.5小时",
    imageUrl:
      "https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    position: "left",
  },
  // 接着是交通方式
  {
    id: "transport-1",
    type: "transportation",
    time: "10:30 - 10:45",
    icon: "🚕",
    duration: "15分钟",
    detail: "打车从迎宾馆南门到外滩观光隧道入口",
  },
  // 外滩景点
  {
    id: "the-bund",
    type: "attraction",
    time: "11:00 - 13:00",
    name: "外滩",
    description:
      "外滩是上海最著名的商业街区之一，汇集了各类商场、餐厅和历史建筑。",
    ticketPrice: "免费",
    recommendedStay: "2小时",
    imageUrl:
      "https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    position: "right",
  },
  // 地铁交通
  {
    id: "transport-2",
    type: "transportation",
    time: "13:00 - 13:20",
    icon: "🚇",
    duration: "20分钟",
    detail: "地铁2号线从南京东路站到人民广场站",
  },
  // 豪生花园
  {
    id: "howard-garden",
    type: "attraction",
    time: "13:30 - 15:00",
    name: "豪生花园",
    description:
      "豪生花园是上海最大的花园之一，园内植物种类丰富，环境幽雅，是休闲游玩的好去处。",
    ticketPrice: "¥50",
    recommendedStay: "1.5小时",
    imageUrl:
      "https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80",
    position: "left",
  },
  // 步行
  {
    id: "transport-3",
    type: "transportation",
    time: "15:00 - 15:10",
    icon: "🚶",
    duration: "10分钟",
    detail: "步行从豪生花园西门到田子坊北入口",
  },
  // 田子坊
  {
    id: "tianzifang",
    type: "attraction",
    time: "15:30 - 17:30",
    name: "田子坊",
    description:
      "田子坊是上海历史悠久的街区，保存了大量的石库门建筑，充满浓厚的文化氛围。",
    ticketPrice: "免费",
    recommendedStay: "2小时",
    imageUrl:
      "https://images.unsplash.com/photo-1598511726623-d3e9f2db00e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    position: "right",
  },
  // 打车
  {
    id: "transport-4",
    type: "transportation",
    time: "17:30 - 17:55",
    icon: "🚕",
    duration: "25分钟",
    detail: "打车从田子坊南门到外滩观景平台",
  },
  // 外滩夜景
  {
    id: "bund-night",
    type: "attraction",
    time: "18:30 - 20:30",
    name: "外滩夜景",
    description:
      "晚上的外滩灯火辨然，汇聚了世界各地的建筑风格，是欣赏上海夜景的最佳地点。",
    ticketPrice: "免费",
    recommendedStay: "2小时",
    imageUrl:
      "https://images.unsplash.com/photo-1536599424071-0b215a388ba7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    position: "left",
  },
]);

/**
 * 行程日期数据
 * 可以从API获取，这里提供默认数据
 */
export const dayItems = reactive<DayItem[]>([
  { date: "2023-07-15", number: 1, label: "第一天" },
  { date: "2023-07-16", number: 2, label: "第二天" },
  { date: "2023-07-17", number: 3, label: "第三天" },
  { date: "2023-07-18", number: 4, label: "第四天" },
  { date: "2023-07-19", number: 5, label: "第五天" },
]);

/**
 * 从API获取旅游指南数据
 * @param cityName 城市名称
 * @param days 天数
 * @param trainInfo 火车票信息
 * @returns 是否成功获取数据
 */
export async function fetchTravelGuide(
  cityName: string,
  days: number = 2,
  trainInfo?: {
    departureTime?: string;
    arrivalTime?: string;
    returnDepartureTime?: string;
    returnArrivalTime?: string;
    departureTrainNumber?: string;
    returnTrainNumber?: string;
  }
): Promise<boolean> {
  console.log(`开始获取旅游指南数据: 城市=${cityName}, 天数=${days}`);

  if (trainInfo) {
    console.log(
      `火车票信息: 去程=${trainInfo.departureTrainNumber}, 返程=${trainInfo.returnTrainNumber}`
    );
  }

  try {
    // 准备火车票信息
    const trainInfoParam = trainInfo
      ? {
          departure_train: trainInfo.departureTrainNumber,
          departure_time: trainInfo.departureTime,
          arrival_time: trainInfo.arrivalTime,
          return_train: trainInfo.returnTrainNumber,
          return_departure_time: trainInfo.returnDepartureTime,
          return_arrival_time: trainInfo.returnArrivalTime,
        }
      : undefined;

    // 调用API获取旅游指南数据
    const guideItems = await travelGuideService.generateGuide({
      city: cityName,
      days: days,
      trainInfo: trainInfoParam,
    });

    // 更新日期数据
    updateDayItems(days);

    console.log(`成功获取旅游指南数据: ${guideItems.length} 个项目`);

    if (guideItems.length > 0) {
      // 清空现有数据
      itineraryItems.splice(0, itineraryItems.length);

      // 如果有火车票信息，添加为交通数据
      if (trainInfo && trainInfo.departureTrainNumber) {
        // 创建去程火车票交通项目
        const departureTrainItem: TransportationItem = {
          id: `train-departure-${trainInfo.departureTrainNumber}`,
          type: "transportation",
          time: trainInfo.departureTime || "未知时间",
          icon: "🚄",
          duration: trainInfo.arrivalTime
            ? `到达时间: ${trainInfo.arrivalTime}`
            : "未知时长",
          detail: `乘坐 ${trainInfo.departureTrainNumber} 次列车前往目的地`,
        };

        // 将去程火车票添加到行程的最前面
        guideItems.unshift(departureTrainItem);
      }

      // 添加API返回的行程数据
      guideItems.forEach((item) => {
        itineraryItems.push(item);
      });

      // 如果有返程火车票信息，添加到行程的最后面
      if (trainInfo && trainInfo.returnTrainNumber) {
        // 创建返程火车票交通项目
        const returnTrainItem: TransportationItem = {
          id: `train-return-${trainInfo.returnTrainNumber}`,
          type: "transportation",
          time: trainInfo.returnDepartureTime || "未知时间",
          icon: "🚄",
          duration: trainInfo.returnArrivalTime
            ? `到达时间: ${trainInfo.returnArrivalTime}`
            : "未知时长",
          detail: `乘坐 ${trainInfo.returnTrainNumber} 次列车返回出发地`,
        };

        // 将返程火车票添加到行程的最后面
        itineraryItems.push(returnTrainItem);
      }

      // 按天数分组行程数据
      organizeItineraryByDay(days);

      return true;
    } else {
      console.warn("API返回的旅游指南数据为空");
      return false;
    }
  } catch (error) {
    console.error("获取旅游指南数据失败:", error);
    return false;
  }
}

/**
 * 按天数分组行程数据
 * @param days 天数
 */
function organizeItineraryByDay(days: number) {
  // 清空现有的每日行程数据
  dailyItineraries = {};

  // 如果只有一天，所有行程都在第一天
  if (days === 1) {
    dailyItineraries[0] = [...itineraryItems];
    return;
  }

  // 查找火车票交通项目
  const departureTrainItem = itineraryItems.find(
    (item) =>
      item.type === "transportation" && item.id.startsWith("train-departure-")
  );

  const returnTrainItem = itineraryItems.find(
    (item) =>
      item.type === "transportation" && item.id.startsWith("train-return-")
  );

  // 创建不包含火车票的行程项目数组
  const regularItems = itineraryItems.filter(
    (item) =>
      !(
        item.type === "transportation" &&
        (item.id.startsWith("train-departure-") ||
          item.id.startsWith("train-return-"))
      )
  );

  // 如果有多天，尝试根据行程项目的时间或其他特征将其分配到不同的天数
  // 这里我们使用一个简单的策略：平均分配行程项目到每一天

  // 计算每天大约有多少个行程项目
  const itemsPerDay = Math.ceil(regularItems.length / days);

  // 分配行程项目到每一天
  for (let day = 0; day < days; day++) {
    const startIndex = day * itemsPerDay;
    const endIndex = Math.min(startIndex + itemsPerDay, regularItems.length);

    // 如果这一天没有行程项目，初始化为空数组
    if (startIndex >= regularItems.length) {
      dailyItineraries[day] = [];
      continue;
    }

    // 分配行程项目
    dailyItineraries[day] = regularItems.slice(startIndex, endIndex);

    // 如果是第一天且有去程火车票，添加到第一天行程的最前面
    if (day === 0 && departureTrainItem) {
      dailyItineraries[day].unshift(departureTrainItem);
    }

    // 如果是最后一天且有返程火车票，添加到最后一天行程的最后面
    if (day === days - 1 && returnTrainItem) {
      dailyItineraries[day].push(returnTrainItem);
    }

    console.log(`第${day + 1}天的行程: ${dailyItineraries[day].length}个项目`);
  }
}

/**
 * 更新日期数据
 * @param days 天数
 */
function updateDayItems(days: number) {
  console.log(`更新日期数据，天数: ${days}`);

  // 清空现有数据
  dayItems.splice(0, dayItems.length);

  // 生成新的日期数据
  const today = new Date();

  // 确保天数是有效的
  const validDays = Math.max(1, Math.min(days, 15)); // 最少1天，最多15天

  for (let i = 0; i < validDays; i++) {
    const date = new Date(today);
    date.setDate(today.getDate() + i);

    const dateStr = date.toISOString().split("T")[0]; // 格式为 YYYY-MM-DD
    const dayNumber = i + 1;
    const label = `第${dayNumber}天`;

    dayItems.push({
      date: dateStr,
      number: dayNumber,
      label: label,
    });
  }

  console.log(`更新后的日期数据: ${dayItems.length}天`);
}

// 不同日期的行程数据
// 全局变量，可以被多个函数访问
export let dailyItineraries: { [key: number]: ItineraryItem[] } = {
  // 第一天的行程就是默认的 itineraryItems
  0: [],
};

// 组合式函数：时间线内容更新逻辑
export function useTimelineContent() {
  const timelineContentRef = ref<HTMLElement | null>(null);
  const isLoading = ref(false);
  const loadingText = ref("正在生成行程计划...");

  // 初始化每日行程数据
  if (dailyItineraries[0].length === 0 && itineraryItems.length > 0) {
    dailyItineraries[0] = [...itineraryItems];
  }

  // 更新行程内容
  const updateItinerary = (dayNumber: string) => {
    if (!timelineContentRef.value) return;

    // 添加一个简单的动画效果
    timelineContentRef.value.style.opacity = "0.5";

    setTimeout(() => {
      // 获取选中的日期对应的行程数据
      const dayNum = parseInt(dayNumber) - 1;
      const dayItinerary = dailyItineraries[dayNum] || [];

      // 如果没有该天的行程数据，可以显示一个提示
      if (dayItinerary.length === 0) {
        console.log(`第${dayNumber}天的行程尚未规划`);
        // 这里可以添加一些UI提示

        // 清空现有数据，显示空行程
        itineraryItems.splice(0, itineraryItems.length);

        // 添加一个提示项目
        itineraryItems.push({
          id: `empty-day-${dayNum}`,
          type: "attraction",
          time: "全天",
          name: "暂无行程",
          description: `第${dayNumber}天的行程尚未规划，请选择其他日期查看。`,
          ticketPrice: "无",
          recommendedStay: "无",
          imageUrl:
            "https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
          position: "left",
        });
      } else {
        console.log(`显示第${dayNumber}天的行程: ${dayItinerary.length}个项目`);

        // 更新整个行程列表
        // 清空现有数据
        itineraryItems.splice(0, itineraryItems.length);

        // 添加选中日期的行程数据
        dayItinerary.forEach((item) => {
          itineraryItems.push(item);
        });
      }

      timelineContentRef.value!.style.opacity = "1";
    }, 300);
  };

  // 这里可以添加其他辅助函数

  return {
    timelineContentRef,
    updateItinerary,
    isLoading,
    loadingText,
  };
}

// 组合式函数：卡片交互逻辑
export function useCardInteraction() {
  // 处理卡片展开/收起
  const toggleCardExpand = (card: HTMLElement) => {
    card.classList.toggle("expanded");

    // 更新展开图标
    const expandIcon = card.querySelector(".expand-icon");
    if (expandIcon) {
      if (card.classList.contains("expanded")) {
        expandIcon.textContent = "▲";
      } else {
        expandIcon.textContent = "▼";
      }
    }
  };

  // 处理图标点击
  const handleIconClick = (event: Event, card: HTMLElement) => {
    event.stopPropagation(); // 阻止事件冒泡
    toggleCardExpand(card);
  };

  // 处理交通方式点击
  const toggleTransportation = (transportation: HTMLElement) => {
    transportation.classList.toggle("expanded");
  };

  return {
    toggleCardExpand,
    handleIconClick,
    toggleTransportation,
  };
}

// 组合式函数：日期选择逻辑
export function useDaySelection() {
  const daySelectorRef = ref<HTMLElement | null>(null);
  const selectedDay = ref<string>("1");

  // 检查某个日期是否被选中
  const isDaySelected = (dayNumber: number) => {
    return selectedDay.value === dayNumber.toString();
  };

  // 处理日期选择
  const handleDaySelect = (
    dayNumber: number,
    updateItinerary: (dayNumber: string) => void
  ) => {
    // 更新选中的日期
    selectedDay.value = dayNumber.toString();

    // 更新时间线内容
    updateItinerary(dayNumber.toString());
  };

  return {
    daySelectorRef,
    selectedDay,
    handleDaySelect,
    isDaySelected,
  };
}

// 组合式函数：组件初始化逻辑
export function useItineraryPlanInit(
  timelineContentUtils: ReturnType<typeof useTimelineContent>,
  _cardInteractionUtils: ReturnType<typeof useCardInteraction>, // 使用下划线前缀表示未使用的参数
  daySelectionUtils: ReturnType<typeof useDaySelection>
) {
  // 解构需要的属性
  const { updateItinerary, isLoading, loadingText } = timelineContentUtils;
  const { daySelectorRef, selectedDay } = daySelectionUtils;

  /**
   * 初始化组件
   * @param cityName 可选的城市名称
   * @param selectedDates 可选的选择日期
   * @param departureTrainInfo 可选的去程火车信息
   * @param returnTrainInfo 可选的返程火车信息
   */
  const initializeComponent = async (
    cityName?: string,
    selectedDates?: string[],
    departureTrainInfo?: {
      trainNumber: string;
      departureTime: string;
      arrivalTime: string;
    },
    returnTrainInfo?: {
      trainNumber: string;
      departureTime: string;
      arrivalTime: string;
    }
  ) => {
    // 设置加载状态
    loadingText.value = `正在为您生成${cityName || "上海"}的行程计划...`;
    isLoading.value = true;

    try {
      // 确定城市名称和天数
      const effectiveCityName = cityName || "上海";
      console.log(`初始化行程计划组件，城市: ${effectiveCityName}`);

      // 计算天数
      let days = 2; // 默认2天
      if (selectedDates && selectedDates.length === 2) {
        // 如果提供了日期范围，计算天数
        const startDate = new Date(selectedDates[0]);
        const endDate = new Date(selectedDates[1]);
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        days = diffDays + 1; // 包含首尾两天
      }

      // 准备火车票信息
      const trainInfo = departureTrainInfo
        ? {
            departureTrainNumber: departureTrainInfo.trainNumber,
            departureTime: departureTrainInfo.departureTime,
            arrivalTime: departureTrainInfo.arrivalTime,
            returnTrainNumber: returnTrainInfo?.trainNumber,
            returnDepartureTime: returnTrainInfo?.departureTime,
            returnArrivalTime: returnTrainInfo?.arrivalTime,
          }
        : undefined;

      // 如果有火车票信息，打印出来
      if (trainInfo) {
        console.log(
          `使用火车票信息生成行程: 去程=${trainInfo.departureTrainNumber}, 返程=${trainInfo.returnTrainNumber}`
        );
      }

      // 每次初始化时都从API获取数据
      console.log(
        `强制从API获取旅游指南数据: 城市=${effectiveCityName}, 天数=${days}`
      );
      const success = await fetchTravelGuide(
        effectiveCityName,
        days,
        trainInfo
      );

      if (!success) {
        console.warn("获取旅游指南数据失败，使用默认数据");
        // 如果API获取失败且没有现有数据，使用默认的上海数据
        if (itineraryItems.length === 0) {
          console.log("没有现有数据，使用默认的上海数据");
          await fetchTravelGuide("上海", 2);
        }
      }
    } catch (error) {
      console.error("初始化组件时出错:", error);
    } finally {
      // 无论成功与否，都重置加载状态
      isLoading.value = false;
    }
    // 初始化完成后，默认显示第一天的行程
    if (daySelectorRef.value) {
      // 默认选中第一个日期项
      const firstDayItem = daySelectorRef.value.querySelector(".day-item");
      if (firstDayItem) {
        firstDayItem.classList.add("selected");
        const dayNumber =
          (firstDayItem as HTMLElement).querySelector(".day-number")
            ?.textContent || "1";
        selectedDay.value = dayNumber;

        // 更新时间线内容，显示第一天的行程
        updateItinerary(dayNumber);
      }
    }

    // 重置选中状态，默认选中第一天
    selectedDay.value = "1";

    // 更新时间线内容，显示第一天的行程
    updateItinerary("1");
  };

  return {
    initializeComponent,
  };
}
