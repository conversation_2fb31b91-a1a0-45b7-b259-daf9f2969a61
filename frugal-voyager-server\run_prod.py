import uvicorn
from pathlib import Path
import os

if __name__ == "__main__":
    # 确保日志目录存在
    Path("logs").mkdir(exist_ok=True)

    # 设置生产环境变量
    os.environ["ENV"] = "production"
    print("🚀 启动生产模式 - Swagger文档已禁用")

    # 获取环境变量或使用默认值
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", "8000"))
    workers = int(os.getenv("WORKERS", "2"))  # 生产环境使用多个worker

    print(f"📡 服务器配置: {host}:{port} (workers: {workers})")

    # 运行应用程序 - 生产模式
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=False,  # 生产环境禁用热重载
        log_level="info",
        workers=workers,
    )
