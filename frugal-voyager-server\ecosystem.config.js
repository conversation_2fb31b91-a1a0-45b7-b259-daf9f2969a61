module.exports = {
  apps: [
    {
      name: "frugal-voyager-server",
      script: "uv",
      args: "run run_prod.py",
      interpreter: null,
      instances: 1,
      exec_mode: "fork",
      watch: false,
      max_memory_restart: "500M",
      env: {
        ENV: "production",
        API_V1_STR: "/api/v1",
        PROJECT_NAME: "省钱旅行者 API",
        PROJECT_DESCRIPTION: "省钱旅行者旅游推荐应用的API",
        VERSION: "0.1.0",
        BACKEND_CORS_ORIGINS: "http://localhost:5173,http://trip.guijimada.cn",
        LOG_LEVEL: "INFO",
        HOST: "0.0.0.0",
        PORT: "8000",
        WORKERS: "2",
        // MCP服务器配置 - Linux生产环境
        MCP_OS_TYPE: "linux",
        MCP_LINUX_COMMAND: "npx",
        MCP_LINUX_ARGS: "-y,@amap/amap-maps-mcp-server",
      },
      error_file: "logs/pm2_error.log",
      out_file: "logs/pm2_output.log",
      merge_logs: true,
      log_date_format: "YYYY-MM-DD HH:mm:ss Z",
      time: true,
    },
  ],
};
