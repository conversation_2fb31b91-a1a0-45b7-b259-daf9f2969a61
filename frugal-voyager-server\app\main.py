from fastapi import FastAPI, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from loguru import logger
from contextlib import asynccontextmanager
import traceback

from app.config import settings
from app.api.v1.api import api_router
from app.utils.logging import setup_logging
from app.services.mcp_service import MCPService
from app.schemas.errors import ErrorResponse, ErrorDetail, ErrorCode
from app.middleware import OpenRouterMiddleware, CustomCORSMiddleware

from fastapi.middleware import Middleware
from starlette.middleware import Middleware
from starlette.middleware.trustedhost import TrustedHostMiddleware

# 配置日志
setup_logging()


# 定义应用生命周期管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    # 应用启动时的操作
    logger.info("应用启动...")
    # 初始化MCP服务的进程池
    MCPService.initialize()
    logger.info("MCP服务初始化完成")

    yield

    # 应用关闭时的操作
    logger.info("应用关闭...")
    # 清理MCP服务的进程池
    await MCPService.cleanup()


# 创建FastAPI应用
# 在生产环境下禁用Swagger文档
docs_url = "/docs" if settings.ENV != "production" else None
redoc_url = "/redoc" if settings.ENV != "production" else None
openapi_url = (
    f"{settings.API_V1_STR}/openapi.json" if settings.ENV != "production" else None
)

app = FastAPI(
    title=settings.PROJECT_NAME,
    description=settings.PROJECT_DESCRIPTION,
    version=settings.VERSION,
    docs_url=docs_url,
    redoc_url=redoc_url,
    openapi_url=openapi_url,
    lifespan=lifespan,
    middleware=[
        Middleware(
            TrustedHostMiddleware, allowed_hosts=["trip.guijimada.cn"]
        )  # 替换为实际域名
    ],
)

# 记录Swagger文档状态
if settings.ENV == "production":
    logger.info("生产环境：Swagger文档已禁用")
else:
    logger.info(f"开发环境：Swagger文档可访问 - /docs, /redoc")

# 设置CORS中间件
# 直接允许所有来源，这是最宽松的CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,  # 当allow_origins=["*"]时，必须设置为False
    allow_methods=["*"],
    allow_headers=["*"],
)
logger.warning("CORS配置允许所有来源，这在生产环境中可能不安全")

# 添加自定义CORS中间件（必须在最外层添加）
app.add_middleware(CustomCORSMiddleware)

# 添加OpenRouter中间件
app.add_middleware(OpenRouterMiddleware)


# 添加验证错误处理器
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """处理请求验证错误。"""
    logger.warning(f"请求验证错误: {exc}")

    # 错误代码
    error_code = ErrorCode.INVALID_REQUEST

    # 构建错误响应
    error_response = ErrorResponse(
        status_code=400,
        message="请求参数无效",
        error_code=error_code,
    )

    # 根据环境提供不同级别的错误详情
    if settings.ENV != "production":
        # 非生产环境：提供详细的验证错误信息
        details = []
        for error in exc.errors():
            details.append(
                ErrorDetail(
                    loc=error.get("loc", []),
                    msg=error.get("msg", ""),
                    type=error.get("type", ""),
                )
            )
        error_response.details = details
    else:
        # 生产环境：提供简化的错误信息
        # 只返回字段名称和简单错误消息，不返回详细的验证错误类型
        simplified_details = []
        for error in exc.errors():
            loc = error.get("loc", [])
            if len(loc) > 1:  # 确保有字段名
                field = loc[-1]  # 获取字段名
                simplified_details.append(f"字段 '{field}' 无效")

        if simplified_details:
            error_response.details = simplified_details

    return JSONResponse(
        status_code=400,
        content=error_response.model_dump(),
    )


# 添加HTTP异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """处理HTTP异常。"""
    logger.warning(f"HTTP异常: {exc.status_code} - {exc.detail}")

    # 获取错误代码
    error_code = getattr(exc, "error_code", None)

    # 构建错误响应
    error_response = ErrorResponse(
        status_code=exc.status_code,
        message=str(exc.detail),
        error_code=error_code,
    )

    # 在非生产环境中，添加错误代码的描述（如果有）
    if settings.ENV != "production" and error_code:
        error_response.details = ErrorCode.get_description(error_code)

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(),
    )


# 添加全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """处理所有未捕获的异常。"""
    # 记录详细错误信息和堆栈跟踪
    logger.error(f"未处理的异常: {exc}")
    logger.error(traceback.format_exc())

    # 错误代码
    error_code = ErrorCode.INTERNAL_SERVER_ERROR

    # 构建错误响应
    error_response = ErrorResponse(
        status_code=500,
        message="服务器内部错误",
        error_code=error_code,
    )

    # 根据环境提供不同级别的错误详情
    if settings.ENV == "development":
        # 开发环境：提供详细错误信息
        error_response.details = {
            "exception": str(exc),
            "type": exc.__class__.__name__,
            "description": ErrorCode.get_description(error_code),
        }
    elif settings.ENV == "testing":
        # 测试环境：提供错误类型和描述
        error_response.details = {
            "type": exc.__class__.__name__,
            "description": ErrorCode.get_description(error_code),
        }
    # 生产环境：不提供详细信息

    return JSONResponse(
        status_code=500,
        content=error_response.model_dump(),
    )


# 挂载API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


# 根端点
@app.get("/")
async def root():
    """
    返回欢迎信息的根端点。
    """
    logger.info("根端点被访问")
    response = {
        "message": "欢迎使用Frugal Voyager API",
        "version": settings.VERSION,
    }

    # 只在非生产环境下提供文档链接
    if settings.ENV != "production":
        response["docs"] = "/docs"
        response["redoc"] = "/redoc"

    return response
