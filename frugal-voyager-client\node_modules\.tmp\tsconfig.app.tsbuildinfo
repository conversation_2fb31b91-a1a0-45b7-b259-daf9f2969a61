{"root": ["../../src/main.ts", "../../src/vite-env.d.ts", "../../src/api/index.ts", "../../src/api/instance.ts", "../../src/api/types.ts", "../../src/api/services/city-service.ts", "../../src/api/services/index.ts", "../../src/api/services/ticket-service.ts", "../../src/api/services/travel-guide-service.ts", "../../src/api/types/city.ts", "../../src/api/types/index.ts", "../../src/api/types/ticket.ts", "../../src/api/types/travel-guide.ts", "../../src/api/utils/errorcodes.ts", "../../src/api/utils/errorhandler.ts", "../../src/api/utils/errornotification.ts", "../../src/api/utils/index.ts", "../../src/components/destinationselectionview/destinationselectionview.ts", "../../src/components/itineraryplanview/itineraryplanview.ts", "../../src/components/timeselectionview/timeselectionview.ts", "../../src/composables/usecomponentrefs.ts", "../../src/composables/usedestinationanddateselection.ts", "../../src/composables/useelementanimation.ts", "../../src/composables/useiteminteraction.ts", "../../src/composables/useitemselection.ts", "../../src/composables/usenotification.ts", "../../src/composables/usescrollcontainer.ts", "../../src/composables/usescrollablecontainer.ts", "../../src/composables/useselectionstate.ts", "../../src/composables/usestepmanager.ts", "../../src/router/index.ts", "../../src/services/city-validation-service.ts", "../../src/services/notification-service.ts", "../../src/app.vue", "../../src/components/destinationselectionview/destinationselectionview.vue", "../../src/components/itineraryplanview/itineraryplanview.vue", "../../src/components/notificationpopup/notificationpopup.vue", "../../src/components/timeselectionview/timeselectionview.vue", "../../src/views/mainview.vue", "../../src/views/settingsview.vue"], "version": "5.7.3"}