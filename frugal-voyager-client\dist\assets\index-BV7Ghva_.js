var La=Object.defineProperty;var Ia=(e,t,n)=>t in e?La(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var wn=(e,t,n)=>Ia(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))s(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&s(i)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function s(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Vr(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const me={},fn=[],vt=()=>{},ka=()=>!1,xs=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),qr=e=>e.startsWith("onUpdate:"),Ae=Object.assign,zr=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Na=Object.prototype.hasOwnProperty,ue=(e,t)=>Na.call(e,t),G=Array.isArray,dn=e=>Rs(e)==="[object Map]",Pi=e=>Rs(e)==="[object Set]",X=e=>typeof e=="function",we=e=>typeof e=="string",Ht=e=>typeof e=="symbol",ye=e=>e!==null&&typeof e=="object",Di=e=>(ye(e)||X(e))&&X(e.then)&&X(e.catch),Mi=Object.prototype.toString,Rs=e=>Mi.call(e),ja=e=>Rs(e).slice(8,-1),$i=e=>Rs(e)==="[object Object]",Kr=e=>we(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Dn=Vr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Cs=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Fa=/-(\w)/g,nt=Cs(e=>e.replace(Fa,(t,n)=>n?n.toUpperCase():"")),Ha=/\B([A-Z])/g,Bt=Cs(e=>e.replace(Ha,"-$1").toLowerCase()),As=Cs(e=>e.charAt(0).toUpperCase()+e.slice(1)),Ws=Cs(e=>e?`on${As(e)}`:""),jt=(e,t)=>!Object.is(e,t),ls=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Li=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},mr=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ba=e=>{const t=we(e)?Number(e):NaN;return isNaN(t)?e:t};let po;const Os=()=>po||(po=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Wr(e){if(G(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=we(s)?za(s):Wr(s);if(r)for(const o in r)t[o]=r[o]}return t}else if(we(e)||ye(e))return e}const Ua=/;(?![^(]*\))/g,Va=/:([^]+)/,qa=/\/\*[^]*?\*\//g;function za(e){const t={};return e.replace(qa,"").split(Ua).forEach(n=>{if(n){const s=n.split(Va);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function je(e){let t="";if(we(e))t=e;else if(G(e))for(let n=0;n<e.length;n++){const s=je(e[n]);s&&(t+=s+" ")}else if(ye(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const Ka="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Wa=Vr(Ka);function Ii(e){return!!e||e===""}const ki=e=>!!(e&&e.__v_isRef===!0),Y=e=>we(e)?e:e==null?"":G(e)||ye(e)&&(e.toString===Mi||!X(e.toString))?ki(e)?Y(e.value):JSON.stringify(e,Ni,2):String(e),Ni=(e,t)=>ki(t)?Ni(e,t.value):dn(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r],o)=>(n[Gs(s,o)+" =>"]=r,n),{})}:Pi(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>Gs(n))}:Ht(t)?Gs(t):ye(t)&&!G(t)&&!$i(t)?String(t):t,Gs=(e,t="")=>{var n;return Ht(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Je;class ji{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Je,!t&&Je&&(this.index=(Je.scopes||(Je.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=Je;try{return Je=this,t()}finally{Je=n}}}on(){Je=this}off(){Je=this.parent}stop(t){if(this._active){this._active=!1;let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(this.effects.length=0,n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.cleanups.length=0,this.scopes){for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function Ga(e){return new ji(e)}function Ja(){return Je}let ve;const Js=new WeakSet;class Fi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Je&&Je.active&&Je.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Js.has(this)&&(Js.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Bi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ho(this),Ui(this);const t=ve,n=rt;ve=this,rt=!0;try{return this.fn()}finally{Vi(this),ve=t,rt=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Yr(t);this.deps=this.depsTail=void 0,ho(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Js.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){gr(this)&&this.run()}get dirty(){return gr(this)}}let Hi=0,Mn,$n;function Bi(e,t=!1){if(e.flags|=8,t){e.next=$n,$n=e;return}e.next=Mn,Mn=e}function Gr(){Hi++}function Jr(){if(--Hi>0)return;if($n){let t=$n;for($n=void 0;t;){const n=t.next;t.next=void 0,t.flags&=-9,t=n}}let e;for(;Mn;){let t=Mn;for(Mn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Ui(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Vi(e){let t,n=e.depsTail,s=n;for(;s;){const r=s.prevDep;s.version===-1?(s===n&&(n=r),Yr(s),Ya(s)):t=s,s.dep.activeLink=s.prevActiveLink,s.prevActiveLink=void 0,s=r}e.deps=t,e.depsTail=n}function gr(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(qi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function qi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Bn))return;e.globalVersion=Bn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!gr(e)){e.flags&=-3;return}const n=ve,s=rt;ve=e,rt=!0;try{Ui(e);const r=e.fn(e._value);(t.version===0||jt(r,e._value))&&(e._value=r,t.version++)}catch(r){throw t.version++,r}finally{ve=n,rt=s,Vi(e),e.flags&=-3}}function Yr(e,t=!1){const{dep:n,prevSub:s,nextSub:r}=e;if(s&&(s.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s,!s&&n.computed)){n.computed.flags&=-5;for(let o=n.computed.deps;o;o=o.nextDep)Yr(o,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Ya(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let rt=!0;const zi=[];function Ut(){zi.push(rt),rt=!1}function Vt(){const e=zi.pop();rt=e===void 0?!0:e}function ho(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=ve;ve=void 0;try{t()}finally{ve=n}}}let Bn=0;class Qa{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Qr{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!ve||!rt||ve===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==ve)n=this.activeLink=new Qa(ve,this),ve.deps?(n.prevDep=ve.depsTail,ve.depsTail.nextDep=n,ve.depsTail=n):ve.deps=ve.depsTail=n,Ki(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=ve.depsTail,n.nextDep=void 0,ve.depsTail.nextDep=n,ve.depsTail=n,ve.deps===n&&(ve.deps=s)}return n}trigger(t){this.version++,Bn++,this.notify(t)}notify(t){Gr();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Jr()}}}function Ki(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ki(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const vr=new WeakMap,Zt=Symbol(""),yr=Symbol(""),Un=Symbol("");function Pe(e,t,n){if(rt&&ve){let s=vr.get(e);s||vr.set(e,s=new Map);let r=s.get(n);r||(s.set(n,r=new Qr),r.map=s,r.key=n),r.track()}}function wt(e,t,n,s,r,o){const i=vr.get(e);if(!i){Bn++;return}const l=a=>{a&&a.trigger()};if(Gr(),t==="clear")i.forEach(l);else{const a=G(e),u=a&&Kr(n);if(a&&n==="length"){const c=Number(s);i.forEach((f,d)=>{(d==="length"||d===Un||!Ht(d)&&d>=c)&&l(f)})}else switch((n!==void 0||i.has(void 0))&&l(i.get(n)),u&&l(i.get(Un)),t){case"add":a?u&&l(i.get("length")):(l(i.get(Zt)),dn(e)&&l(i.get(yr)));break;case"delete":a||(l(i.get(Zt)),dn(e)&&l(i.get(yr)));break;case"set":dn(e)&&l(i.get(Zt));break}}Jr()}function ln(e){const t=le(e);return t===e?t:(Pe(t,"iterate",Un),et(e)?t:t.map(De))}function Ps(e){return Pe(e=le(e),"iterate",Un),e}const Xa={__proto__:null,[Symbol.iterator](){return Ys(this,Symbol.iterator,De)},concat(...e){return ln(this).concat(...e.map(t=>G(t)?ln(t):t))},entries(){return Ys(this,"entries",e=>(e[1]=De(e[1]),e))},every(e,t){return yt(this,"every",e,t,void 0,arguments)},filter(e,t){return yt(this,"filter",e,t,n=>n.map(De),arguments)},find(e,t){return yt(this,"find",e,t,De,arguments)},findIndex(e,t){return yt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return yt(this,"findLast",e,t,De,arguments)},findLastIndex(e,t){return yt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return yt(this,"forEach",e,t,void 0,arguments)},includes(...e){return Qs(this,"includes",e)},indexOf(...e){return Qs(this,"indexOf",e)},join(e){return ln(this).join(e)},lastIndexOf(...e){return Qs(this,"lastIndexOf",e)},map(e,t){return yt(this,"map",e,t,void 0,arguments)},pop(){return Tn(this,"pop")},push(...e){return Tn(this,"push",e)},reduce(e,...t){return mo(this,"reduce",e,t)},reduceRight(e,...t){return mo(this,"reduceRight",e,t)},shift(){return Tn(this,"shift")},some(e,t){return yt(this,"some",e,t,void 0,arguments)},splice(...e){return Tn(this,"splice",e)},toReversed(){return ln(this).toReversed()},toSorted(e){return ln(this).toSorted(e)},toSpliced(...e){return ln(this).toSpliced(...e)},unshift(...e){return Tn(this,"unshift",e)},values(){return Ys(this,"values",De)}};function Ys(e,t,n){const s=Ps(e),r=s[t]();return s!==e&&!et(e)&&(r._next=r.next,r.next=()=>{const o=r._next();return o.value&&(o.value=n(o.value)),o}),r}const Za=Array.prototype;function yt(e,t,n,s,r,o){const i=Ps(e),l=i!==e&&!et(e),a=i[t];if(a!==Za[t]){const f=a.apply(e,o);return l?De(f):f}let u=n;i!==e&&(l?u=function(f,d){return n.call(this,De(f),d,e)}:n.length>2&&(u=function(f,d){return n.call(this,f,d,e)}));const c=a.call(i,u,s);return l&&r?r(c):c}function mo(e,t,n,s){const r=Ps(e);let o=n;return r!==e&&(et(e)?n.length>3&&(o=function(i,l,a){return n.call(this,i,l,a,e)}):o=function(i,l,a){return n.call(this,i,De(l),a,e)}),r[t](o,...s)}function Qs(e,t,n){const s=le(e);Pe(s,"iterate",Un);const r=s[t](...n);return(r===-1||r===!1)&&eo(n[0])?(n[0]=le(n[0]),s[t](...n)):r}function Tn(e,t,n=[]){Ut(),Gr();const s=le(e)[t].apply(e,n);return Jr(),Vt(),s}const ec=Vr("__proto__,__v_isRef,__isVue"),Wi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Ht));function tc(e){Ht(e)||(e=String(e));const t=le(this);return Pe(t,"has",e),t.hasOwnProperty(e)}class Gi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){if(n==="__v_skip")return t.__v_skip;const r=this._isReadonly,o=this._isShallow;if(n==="__v_isReactive")return!r;if(n==="__v_isReadonly")return r;if(n==="__v_isShallow")return o;if(n==="__v_raw")return s===(r?o?fc:Xi:o?Qi:Yi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const i=G(t);if(!r){let a;if(i&&(a=Xa[n]))return a;if(n==="hasOwnProperty")return tc}const l=Reflect.get(t,n,Le(t)?t:s);return(Ht(n)?Wi.has(n):ec(n))||(r||Pe(t,"get",n),o)?l:Le(l)?i&&Kr(n)?l:l.value:ye(l)?r?el(l):Ct(l):l}}class Ji extends Gi{constructor(t=!1){super(!1,t)}set(t,n,s,r){let o=t[n];if(!this._isShallow){const a=sn(o);if(!et(s)&&!sn(s)&&(o=le(o),s=le(s)),!G(t)&&Le(o)&&!Le(s))return a?!1:(o.value=s,!0)}const i=G(t)&&Kr(n)?Number(n)<t.length:ue(t,n),l=Reflect.set(t,n,s,Le(t)?t:r);return t===le(r)&&(i?jt(s,o)&&wt(t,"set",n,s):wt(t,"add",n,s)),l}deleteProperty(t,n){const s=ue(t,n);t[n];const r=Reflect.deleteProperty(t,n);return r&&s&&wt(t,"delete",n,void 0),r}has(t,n){const s=Reflect.has(t,n);return(!Ht(n)||!Wi.has(n))&&Pe(t,"has",n),s}ownKeys(t){return Pe(t,"iterate",G(t)?"length":Zt),Reflect.ownKeys(t)}}class nc extends Gi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const sc=new Ji,rc=new nc,oc=new Ji(!0);const br=e=>e,ss=e=>Reflect.getPrototypeOf(e);function ic(e,t,n){return function(...s){const r=this.__v_raw,o=le(r),i=dn(o),l=e==="entries"||e===Symbol.iterator&&i,a=e==="keys"&&i,u=r[e](...s),c=n?br:t?_r:De;return!t&&Pe(o,"iterate",a?yr:Zt),{next(){const{value:f,done:d}=u.next();return d?{value:f,done:d}:{value:l?[c(f[0]),c(f[1])]:c(f),done:d}},[Symbol.iterator](){return this}}}}function rs(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function lc(e,t){const n={get(r){const o=this.__v_raw,i=le(o),l=le(r);e||(jt(r,l)&&Pe(i,"get",r),Pe(i,"get",l));const{has:a}=ss(i),u=t?br:e?_r:De;if(a.call(i,r))return u(o.get(r));if(a.call(i,l))return u(o.get(l));o!==i&&o.get(r)},get size(){const r=this.__v_raw;return!e&&Pe(le(r),"iterate",Zt),Reflect.get(r,"size",r)},has(r){const o=this.__v_raw,i=le(o),l=le(r);return e||(jt(r,l)&&Pe(i,"has",r),Pe(i,"has",l)),r===l?o.has(r):o.has(r)||o.has(l)},forEach(r,o){const i=this,l=i.__v_raw,a=le(l),u=t?br:e?_r:De;return!e&&Pe(a,"iterate",Zt),l.forEach((c,f)=>r.call(o,u(c),u(f),i))}};return Ae(n,e?{add:rs("add"),set:rs("set"),delete:rs("delete"),clear:rs("clear")}:{add(r){!t&&!et(r)&&!sn(r)&&(r=le(r));const o=le(this);return ss(o).has.call(o,r)||(o.add(r),wt(o,"add",r,r)),this},set(r,o){!t&&!et(o)&&!sn(o)&&(o=le(o));const i=le(this),{has:l,get:a}=ss(i);let u=l.call(i,r);u||(r=le(r),u=l.call(i,r));const c=a.call(i,r);return i.set(r,o),u?jt(o,c)&&wt(i,"set",r,o):wt(i,"add",r,o),this},delete(r){const o=le(this),{has:i,get:l}=ss(o);let a=i.call(o,r);a||(r=le(r),a=i.call(o,r)),l&&l.call(o,r);const u=o.delete(r);return a&&wt(o,"delete",r,void 0),u},clear(){const r=le(this),o=r.size!==0,i=r.clear();return o&&wt(r,"clear",void 0,void 0),i}}),["keys","values","entries",Symbol.iterator].forEach(r=>{n[r]=ic(r,e,t)}),n}function Xr(e,t){const n=lc(e,t);return(s,r,o)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(ue(n,r)&&r in s?n:s,r,o)}const ac={get:Xr(!1,!1)},cc={get:Xr(!1,!0)},uc={get:Xr(!0,!1)};const Yi=new WeakMap,Qi=new WeakMap,Xi=new WeakMap,fc=new WeakMap;function dc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function pc(e){return e.__v_skip||!Object.isExtensible(e)?0:dc(ja(e))}function Ct(e){return sn(e)?e:Zr(e,!1,sc,ac,Yi)}function Zi(e){return Zr(e,!1,oc,cc,Qi)}function el(e){return Zr(e,!0,rc,uc,Xi)}function Zr(e,t,n,s,r){if(!ye(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=r.get(e);if(o)return o;const i=pc(e);if(i===0)return e;const l=new Proxy(e,i===2?s:n);return r.set(e,l),l}function pn(e){return sn(e)?pn(e.__v_raw):!!(e&&e.__v_isReactive)}function sn(e){return!!(e&&e.__v_isReadonly)}function et(e){return!!(e&&e.__v_isShallow)}function eo(e){return e?!!e.__v_raw:!1}function le(e){const t=e&&e.__v_raw;return t?le(t):e}function tl(e){return!ue(e,"__v_skip")&&Object.isExtensible(e)&&Li(e,"__v_skip",!0),e}const De=e=>ye(e)?Ct(e):e,_r=e=>ye(e)?el(e):e;function Le(e){return e?e.__v_isRef===!0:!1}function z(e){return nl(e,!1)}function hc(e){return nl(e,!0)}function nl(e,t){return Le(e)?e:new mc(e,t)}class mc{constructor(t,n){this.dep=new Qr,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:le(t),this._value=n?t:De(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||et(t)||sn(t);t=s?t:le(t),jt(t,n)&&(this._rawValue=t,this._value=s?t:De(t),this.dep.trigger())}}function B(e){return Le(e)?e.value:e}const gc={get:(e,t,n)=>t==="__v_raw"?e:B(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return Le(r)&&!Le(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function sl(e){return pn(e)?e:new Proxy(e,gc)}class vc{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Qr(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Bn-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&ve!==this)return Bi(this,!0),!0}get value(){const t=this.dep.track();return qi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yc(e,t,n=!1){let s,r;return X(e)?s=e:(s=e.get,r=e.set),new vc(s,r,n)}const os={},ps=new WeakMap;let Gt;function bc(e,t=!1,n=Gt){if(n){let s=ps.get(n);s||ps.set(n,s=[]),s.push(e)}}function _c(e,t,n=me){const{immediate:s,deep:r,once:o,scheduler:i,augmentJob:l,call:a}=n,u=O=>r?O:et(O)||r===!1||r===0?Tt(O,1):Tt(O);let c,f,d,p,v=!1,y=!1;if(Le(e)?(f=()=>e.value,v=et(e)):pn(e)?(f=()=>u(e),v=!0):G(e)?(y=!0,v=e.some(O=>pn(O)||et(O)),f=()=>e.map(O=>{if(Le(O))return O.value;if(pn(O))return u(O);if(X(O))return a?a(O,2):O()})):X(e)?t?f=a?()=>a(e,2):e:f=()=>{if(d){Ut();try{d()}finally{Vt()}}const O=Gt;Gt=c;try{return a?a(e,3,[p]):e(p)}finally{Gt=O}}:f=vt,t&&r){const O=f,I=r===!0?1/0:r;f=()=>Tt(O(),I)}const S=Ja(),R=()=>{c.stop(),S&&S.active&&zr(S.effects,c)};if(o&&t){const O=t;t=(...I)=>{O(...I),R()}}let C=y?new Array(e.length).fill(os):os;const D=O=>{if(!(!(c.flags&1)||!c.dirty&&!O))if(t){const I=c.run();if(r||v||(y?I.some((M,P)=>jt(M,C[P])):jt(I,C))){d&&d();const M=Gt;Gt=c;try{const P=[I,C===os?void 0:y&&C[0]===os?[]:C,p];a?a(t,3,P):t(...P),C=I}finally{Gt=M}}}else c.run()};return l&&l(D),c=new Fi(f),c.scheduler=i?()=>i(D,!1):D,p=O=>bc(O,!1,c),d=c.onStop=()=>{const O=ps.get(c);if(O){if(a)a(O,4);else for(const I of O)I();ps.delete(c)}},t?s?D(!0):C=c.run():i?i(D.bind(null,!0),!0):c.run(),R.pause=c.pause.bind(c),R.resume=c.resume.bind(c),R.stop=R,R}function Tt(e,t=1/0,n){if(t<=0||!ye(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Le(e))Tt(e.value,t,n);else if(G(e))for(let s=0;s<e.length;s++)Tt(e[s],t,n);else if(Pi(e)||dn(e))e.forEach(s=>{Tt(s,t,n)});else if($i(e)){for(const s in e)Tt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&Tt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Qn(e,t,n,s){try{return s?e(...s):e()}catch(r){Ds(r,t,n)}}function ot(e,t,n,s){if(X(e)){const r=Qn(e,t,n,s);return r&&Di(r)&&r.catch(o=>{Ds(o,t,n)}),r}if(G(e)){const r=[];for(let o=0;o<e.length;o++)r.push(ot(e[o],t,n,s));return r}}function Ds(e,t,n,s=!0){const r=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||me;if(t){let l=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${n}`;for(;l;){const c=l.ec;if(c){for(let f=0;f<c.length;f++)if(c[f](e,a,u)===!1)return}l=l.parent}if(o){Ut(),Qn(o,null,10,[e,a,u]),Vt();return}}Sc(e,n,r,s,i)}function Sc(e,t,n,s=!0,r=!1){if(r)throw e;console.error(e)}const Fe=[];let ht=-1;const hn=[];let Mt=null,an=0;const rl=Promise.resolve();let hs=null;function ol(e){const t=hs||rl;return e?t.then(this?e.bind(this):e):t}function wc(e){let t=ht+1,n=Fe.length;for(;t<n;){const s=t+n>>>1,r=Fe[s],o=Vn(r);o<e||o===e&&r.flags&2?t=s+1:n=s}return t}function to(e){if(!(e.flags&1)){const t=Vn(e),n=Fe[Fe.length-1];!n||!(e.flags&2)&&t>=Vn(n)?Fe.push(e):Fe.splice(wc(t),0,e),e.flags|=1,il()}}function il(){hs||(hs=rl.then(al))}function Tc(e){G(e)?hn.push(...e):Mt&&e.id===-1?Mt.splice(an+1,0,e):e.flags&1||(hn.push(e),e.flags|=1),il()}function go(e,t,n=ht+1){for(;n<Fe.length;n++){const s=Fe[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Fe.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ll(e){if(hn.length){const t=[...new Set(hn)].sort((n,s)=>Vn(n)-Vn(s));if(hn.length=0,Mt){Mt.push(...t);return}for(Mt=t,an=0;an<Mt.length;an++){const n=Mt[an];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}Mt=null,an=0}}const Vn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function al(e){try{for(ht=0;ht<Fe.length;ht++){const t=Fe[ht];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Qn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;ht<Fe.length;ht++){const t=Fe[ht];t&&(t.flags&=-2)}ht=-1,Fe.length=0,ll(),hs=null,(Fe.length||hn.length)&&al()}}let Ve=null,cl=null;function ms(e){const t=Ve;return Ve=e,cl=e&&e.type.__scopeId||null,t}function ul(e,t=Ve,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Co(-1);const o=ms(t);let i;try{i=e(...r)}finally{ms(o),s._d&&Co(1)}return i};return s._n=!0,s._c=!0,s._d=!0,s}function kt(e,t){if(Ve===null)return e;const n=ks(Ve),s=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[o,i,l,a=me]=t[r];o&&(X(o)&&(o={mounted:o,updated:o}),o.deep&&Tt(i),s.push({dir:o,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function qt(e,t,n,s){const r=e.dirs,o=t&&t.dirs;for(let i=0;i<r.length;i++){const l=r[i];o&&(l.oldValue=o[i].value);let a=l.dir[s];a&&(Ut(),ot(a,n,8,[e.el,l,e,t]),Vt())}}const Ec=Symbol("_vte"),fl=e=>e.__isTeleport,$t=Symbol("_leaveCb"),is=Symbol("_enterCb");function xc(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return on(()=>{e.isMounted=!0}),bl(()=>{e.isUnmounting=!0}),e}const Xe=[Function,Array],dl={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Xe,onEnter:Xe,onAfterEnter:Xe,onEnterCancelled:Xe,onBeforeLeave:Xe,onLeave:Xe,onAfterLeave:Xe,onLeaveCancelled:Xe,onBeforeAppear:Xe,onAppear:Xe,onAfterAppear:Xe,onAppearCancelled:Xe},pl=e=>{const t=e.subTree;return t.component?pl(t.component):t},Rc={name:"BaseTransition",props:dl,setup(e,{slots:t}){const n=Tu(),s=xc();return()=>{const r=t.default&&gl(t.default(),!0);if(!r||!r.length)return;const o=hl(r),i=le(e),{mode:l}=i;if(s.isLeaving)return Xs(o);const a=vo(o);if(!a)return Xs(o);let u=Sr(a,i,s,n,f=>u=f);a.type!==Ue&&qn(a,u);let c=n.subTree&&vo(n.subTree);if(c&&c.type!==Ue&&!Yt(a,c)&&pl(n).type!==Ue){let f=Sr(c,i,s,n);if(qn(c,f),l==="out-in"&&a.type!==Ue)return s.isLeaving=!0,f.afterLeave=()=>{s.isLeaving=!1,n.job.flags&8||n.update(),delete f.afterLeave,c=void 0},Xs(o);l==="in-out"&&a.type!==Ue?f.delayLeave=(d,p,v)=>{const y=ml(s,c);y[String(c.key)]=c,d[$t]=()=>{p(),d[$t]=void 0,delete u.delayedLeave,c=void 0},u.delayedLeave=()=>{v(),delete u.delayedLeave,c=void 0}}:c=void 0}else c&&(c=void 0);return o}}};function hl(e){let t=e[0];if(e.length>1){for(const n of e)if(n.type!==Ue){t=n;break}}return t}const Cc=Rc;function ml(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Sr(e,t,n,s,r){const{appear:o,mode:i,persisted:l=!1,onBeforeEnter:a,onEnter:u,onAfterEnter:c,onEnterCancelled:f,onBeforeLeave:d,onLeave:p,onAfterLeave:v,onLeaveCancelled:y,onBeforeAppear:S,onAppear:R,onAfterAppear:C,onAppearCancelled:D}=t,O=String(e.key),I=ml(n,e),M=(V,se)=>{V&&ot(V,s,9,se)},P=(V,se)=>{const pe=se[1];M(V,se),G(V)?V.every(H=>H.length<=1)&&pe():V.length<=1&&pe()},U={mode:i,persisted:l,beforeEnter(V){let se=a;if(!n.isMounted)if(o)se=S||a;else return;V[$t]&&V[$t](!0);const pe=I[O];pe&&Yt(e,pe)&&pe.el[$t]&&pe.el[$t](),M(se,[V])},enter(V){let se=u,pe=c,H=f;if(!n.isMounted)if(o)se=R||u,pe=C||c,H=D||f;else return;let oe=!1;const Ee=V[is]=Ie=>{oe||(oe=!0,Ie?M(H,[V]):M(pe,[V]),U.delayedLeave&&U.delayedLeave(),V[is]=void 0)};se?P(se,[V,Ee]):Ee()},leave(V,se){const pe=String(e.key);if(V[is]&&V[is](!0),n.isUnmounting)return se();M(d,[V]);let H=!1;const oe=V[$t]=Ee=>{H||(H=!0,se(),Ee?M(y,[V]):M(v,[V]),V[$t]=void 0,I[pe]===e&&delete I[pe])};I[pe]=e,p?P(p,[V,oe]):oe()},clone(V){const se=Sr(V,t,n,s,r);return r&&r(se),se}};return U}function Xs(e){if(Ms(e))return e=Ft(e),e.children=null,e}function vo(e){if(!Ms(e))return fl(e.type)&&e.children?hl(e.children):e;const{shapeFlag:t,children:n}=e;if(n){if(t&16)return n[0];if(t&32&&X(n.default))return n.default()}}function qn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,qn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function gl(e,t=!1,n){let s=[],r=0;for(let o=0;o<e.length;o++){let i=e[o];const l=n==null?i.key:String(n)+String(i.key!=null?i.key:o);i.type===Se?(i.patchFlag&128&&r++,s=s.concat(gl(i.children,t,l))):(t||i.type!==Ue)&&s.push(l!=null?Ft(i,{key:l}):i)}if(r>1)for(let o=0;o<s.length;o++)s[o].patchFlag=-2;return s}/*! #__NO_SIDE_EFFECTS__ */function At(e,t){return X(e)?Ae({name:e.name},t,{setup:e}):e}function vl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function gs(e,t,n,s,r=!1){if(G(e)){e.forEach((v,y)=>gs(v,t&&(G(t)?t[y]:t),n,s,r));return}if(Ln(s)&&!r){s.shapeFlag&512&&s.type.__asyncResolved&&s.component.subTree.component&&gs(e,t,n,s.component.subTree);return}const o=s.shapeFlag&4?ks(s.component):s.el,i=r?null:o,{i:l,r:a}=e,u=t&&t.r,c=l.refs===me?l.refs={}:l.refs,f=l.setupState,d=le(f),p=f===me?()=>!1:v=>ue(d,v);if(u!=null&&u!==a&&(we(u)?(c[u]=null,p(u)&&(f[u]=null)):Le(u)&&(u.value=null)),X(a))Qn(a,l,12,[i,c]);else{const v=we(a),y=Le(a);if(v||y){const S=()=>{if(e.f){const R=v?p(a)?f[a]:c[a]:a.value;r?G(R)&&zr(R,o):G(R)?R.includes(o)||R.push(o):v?(c[a]=[o],p(a)&&(f[a]=c[a])):(a.value=[o],e.k&&(c[e.k]=a.value))}else v?(c[a]=i,p(a)&&(f[a]=i)):y&&(a.value=i,e.k&&(c[e.k]=i))};i?(S.id=-1,Ge(S,n)):S()}}}Os().requestIdleCallback;Os().cancelIdleCallback;const Ln=e=>!!e.type.__asyncLoader,Ms=e=>e.type.__isKeepAlive;function Ac(e,t){yl(e,"a",t)}function Oc(e,t){yl(e,"da",t)}function yl(e,t,n=Oe){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if($s(t,s,n),n){let r=n.parent;for(;r&&r.parent;)Ms(r.parent.vnode)&&Pc(s,t,n,r),r=r.parent}}function Pc(e,t,n,s){const r=$s(t,e,s,!0);Xn(()=>{zr(s[t],r)},n)}function $s(e,t,n=Oe,s=!1){if(n){const r=n[e]||(n[e]=[]),o=t.__weh||(t.__weh=(...i)=>{Ut();const l=Zn(n),a=ot(t,n,e,i);return l(),Vt(),a});return s?r.unshift(o):r.push(o),o}}const Ot=e=>(t,n=Oe)=>{(!Kn||e==="sp")&&$s(e,(...s)=>t(...s),n)},Dc=Ot("bm"),on=Ot("m"),Mc=Ot("bu"),$c=Ot("u"),bl=Ot("bum"),Xn=Ot("um"),Lc=Ot("sp"),Ic=Ot("rtg"),kc=Ot("rtc");function Nc(e,t=Oe){$s("ec",e,t)}const jc="components";function Fc(e,t){return Bc(jc,e,!0,t)||e}const Hc=Symbol.for("v-ndc");function Bc(e,t,n=!0,s=!1){const r=Ve||Oe;if(r){const o=r.type;{const l=Au(o,!1);if(l&&(l===t||l===nt(t)||l===As(nt(t))))return o}const i=yo(r[e]||o[e],t)||yo(r.appContext[e],t);return!i&&s?o:i}}function yo(e,t){return e&&(e[t]||e[nt(t)]||e[As(nt(t))])}function Et(e,t,n,s){let r;const o=n,i=G(e);if(i||we(e)){const l=i&&pn(e);let a=!1;l&&(a=!et(e),e=Ps(e)),r=new Array(e.length);for(let u=0,c=e.length;u<c;u++)r[u]=t(a?De(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){r=new Array(e);for(let l=0;l<e;l++)r[l]=t(l+1,l,void 0,o)}else if(ye(e))if(e[Symbol.iterator])r=Array.from(e,(l,a)=>t(l,a,void 0,o));else{const l=Object.keys(e);r=new Array(l.length);for(let a=0,u=l.length;a<u;a++){const c=l[a];r[a]=t(e[c],c,a,o)}}else r=[];return r}const wr=e=>e?Bl(e)?ks(e):wr(e.parent):null,In=Ae(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>wr(e.parent),$root:e=>wr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Sl(e),$forceUpdate:e=>e.f||(e.f=()=>{to(e.update)}),$nextTick:e=>e.n||(e.n=ol.bind(e.proxy)),$watch:e=>au.bind(e)}),Zs=(e,t)=>e!==me&&!e.__isScriptSetup&&ue(e,t),Uc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:r,props:o,accessCache:i,type:l,appContext:a}=e;let u;if(t[0]!=="$"){const p=i[t];if(p!==void 0)switch(p){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return o[t]}else{if(Zs(s,t))return i[t]=1,s[t];if(r!==me&&ue(r,t))return i[t]=2,r[t];if((u=e.propsOptions[0])&&ue(u,t))return i[t]=3,o[t];if(n!==me&&ue(n,t))return i[t]=4,n[t];Tr&&(i[t]=0)}}const c=In[t];let f,d;if(c)return t==="$attrs"&&Pe(e.attrs,"get",""),c(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(n!==me&&ue(n,t))return i[t]=4,n[t];if(d=a.config.globalProperties,ue(d,t))return d[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:o}=e;return Zs(r,t)?(r[t]=n,!0):s!==me&&ue(s,t)?(s[t]=n,!0):ue(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:o}},i){let l;return!!n[i]||e!==me&&ue(e,i)||Zs(t,i)||(l=o[0])&&ue(l,i)||ue(s,i)||ue(In,i)||ue(r.config.globalProperties,i)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ue(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function bo(e){return G(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Tr=!0;function Vc(e){const t=Sl(e),n=e.proxy,s=e.ctx;Tr=!1,t.beforeCreate&&_o(t.beforeCreate,e,"bc");const{data:r,computed:o,methods:i,watch:l,provide:a,inject:u,created:c,beforeMount:f,mounted:d,beforeUpdate:p,updated:v,activated:y,deactivated:S,beforeDestroy:R,beforeUnmount:C,destroyed:D,unmounted:O,render:I,renderTracked:M,renderTriggered:P,errorCaptured:U,serverPrefetch:V,expose:se,inheritAttrs:pe,components:H,directives:oe,filters:Ee}=t;if(u&&qc(u,s,null),i)for(const ae in i){const ie=i[ae];X(ie)&&(s[ae]=ie.bind(n))}if(r){const ae=r.call(n,n);ye(ae)&&(e.data=Ct(ae))}if(Tr=!0,o)for(const ae in o){const ie=o[ae],st=X(ie)?ie.bind(n,n):X(ie.get)?ie.get.bind(n,n):vt,at=!X(ie)&&X(ie.set)?ie.set.bind(n):vt,ke=Ye({get:st,set:at});Object.defineProperty(s,ae,{enumerable:!0,configurable:!0,get:()=>ke.value,set:_e=>ke.value=_e})}if(l)for(const ae in l)_l(l[ae],s,n,ae);if(a){const ae=X(a)?a.call(n):a;Reflect.ownKeys(ae).forEach(ie=>{en(ie,ae[ie])})}c&&_o(c,e,"c");function fe(ae,ie){G(ie)?ie.forEach(st=>ae(st.bind(n))):ie&&ae(ie.bind(n))}if(fe(Dc,f),fe(on,d),fe(Mc,p),fe($c,v),fe(Ac,y),fe(Oc,S),fe(Nc,U),fe(kc,M),fe(Ic,P),fe(bl,C),fe(Xn,O),fe(Lc,V),G(se))if(se.length){const ae=e.exposed||(e.exposed={});se.forEach(ie=>{Object.defineProperty(ae,ie,{get:()=>n[ie],set:st=>n[ie]=st})})}else e.exposed||(e.exposed={});I&&e.render===vt&&(e.render=I),pe!=null&&(e.inheritAttrs=pe),H&&(e.components=H),oe&&(e.directives=oe),V&&vl(e)}function qc(e,t,n=vt){G(e)&&(e=Er(e));for(const s in e){const r=e[s];let o;ye(r)?"default"in r?o=tt(r.from||s,r.default,!0):o=tt(r.from||s):o=tt(r),Le(o)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>o.value,set:i=>o.value=i}):t[s]=o}}function _o(e,t,n){ot(G(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function _l(e,t,n,s){let r=s.includes(".")?Il(n,s):()=>n[s];if(we(e)){const o=t[e];X(o)&&tn(r,o)}else if(X(e))tn(r,e.bind(n));else if(ye(e))if(G(e))e.forEach(o=>_l(o,t,n,s));else{const o=X(e.handler)?e.handler.bind(n):t[e.handler];X(o)&&tn(r,o,e)}}function Sl(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:o,config:{optionMergeStrategies:i}}=e.appContext,l=o.get(t);let a;return l?a=l:!r.length&&!n&&!s?a=t:(a={},r.length&&r.forEach(u=>vs(a,u,i,!0)),vs(a,t,i)),ye(t)&&o.set(t,a),a}function vs(e,t,n,s=!1){const{mixins:r,extends:o}=t;o&&vs(e,o,n,!0),r&&r.forEach(i=>vs(e,i,n,!0));for(const i in t)if(!(s&&i==="expose")){const l=zc[i]||n&&n[i];e[i]=l?l(e[i],t[i]):t[i]}return e}const zc={data:So,props:wo,emits:wo,methods:An,computed:An,beforeCreate:Ne,created:Ne,beforeMount:Ne,mounted:Ne,beforeUpdate:Ne,updated:Ne,beforeDestroy:Ne,beforeUnmount:Ne,destroyed:Ne,unmounted:Ne,activated:Ne,deactivated:Ne,errorCaptured:Ne,serverPrefetch:Ne,components:An,directives:An,watch:Wc,provide:So,inject:Kc};function So(e,t){return t?e?function(){return Ae(X(e)?e.call(this,this):e,X(t)?t.call(this,this):t)}:t:e}function Kc(e,t){return An(Er(e),Er(t))}function Er(e){if(G(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ne(e,t){return e?[...new Set([].concat(e,t))]:t}function An(e,t){return e?Ae(Object.create(null),e,t):t}function wo(e,t){return e?G(e)&&G(t)?[...new Set([...e,...t])]:Ae(Object.create(null),bo(e),bo(t??{})):t}function Wc(e,t){if(!e)return t;if(!t)return e;const n=Ae(Object.create(null),e);for(const s in t)n[s]=Ne(e[s],t[s]);return n}function wl(){return{app:null,config:{isNativeTag:ka,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Gc=0;function Jc(e,t){return function(s,r=null){X(s)||(s=Ae({},s)),r!=null&&!ye(r)&&(r=null);const o=wl(),i=new WeakSet,l=[];let a=!1;const u=o.app={_uid:Gc++,_component:s,_props:r,_container:null,_context:o,_instance:null,version:Pu,get config(){return o.config},set config(c){},use(c,...f){return i.has(c)||(c&&X(c.install)?(i.add(c),c.install(u,...f)):X(c)&&(i.add(c),c(u,...f))),u},mixin(c){return o.mixins.includes(c)||o.mixins.push(c),u},component(c,f){return f?(o.components[c]=f,u):o.components[c]},directive(c,f){return f?(o.directives[c]=f,u):o.directives[c]},mount(c,f,d){if(!a){const p=u._ceVNode||Re(s,r);return p.appContext=o,d===!0?d="svg":d===!1&&(d=void 0),e(p,c,d),a=!0,u._container=c,c.__vue_app__=u,ks(p.component)}},onUnmount(c){l.push(c)},unmount(){a&&(ot(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,f){return o.provides[c]=f,u},runWithContext(c){const f=mn;mn=u;try{return c()}finally{mn=f}}};return u}}let mn=null;function en(e,t){if(Oe){let n=Oe.provides;const s=Oe.parent&&Oe.parent.provides;s===n&&(n=Oe.provides=Object.create(s)),n[e]=t}}function tt(e,t,n=!1){const s=Oe||Ve;if(s||mn){const r=mn?mn._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&X(t)?t.call(s&&s.proxy):t}}const Tl={},El=()=>Object.create(Tl),xl=e=>Object.getPrototypeOf(e)===Tl;function Yc(e,t,n,s=!1){const r={},o=El();e.propsDefaults=Object.create(null),Rl(e,t,r,o);for(const i in e.propsOptions[0])i in r||(r[i]=void 0);n?e.props=s?r:Zi(r):e.type.props?e.props=r:e.props=o,e.attrs=o}function Qc(e,t,n,s){const{props:r,attrs:o,vnode:{patchFlag:i}}=e,l=le(r),[a]=e.propsOptions;let u=!1;if((s||i>0)&&!(i&16)){if(i&8){const c=e.vnode.dynamicProps;for(let f=0;f<c.length;f++){let d=c[f];if(Ls(e.emitsOptions,d))continue;const p=t[d];if(a)if(ue(o,d))p!==o[d]&&(o[d]=p,u=!0);else{const v=nt(d);r[v]=xr(a,l,v,p,e,!1)}else p!==o[d]&&(o[d]=p,u=!0)}}}else{Rl(e,t,r,o)&&(u=!0);let c;for(const f in l)(!t||!ue(t,f)&&((c=Bt(f))===f||!ue(t,c)))&&(a?n&&(n[f]!==void 0||n[c]!==void 0)&&(r[f]=xr(a,l,f,void 0,e,!0)):delete r[f]);if(o!==l)for(const f in o)(!t||!ue(t,f))&&(delete o[f],u=!0)}u&&wt(e.attrs,"set","")}function Rl(e,t,n,s){const[r,o]=e.propsOptions;let i=!1,l;if(t)for(let a in t){if(Dn(a))continue;const u=t[a];let c;r&&ue(r,c=nt(a))?!o||!o.includes(c)?n[c]=u:(l||(l={}))[c]=u:Ls(e.emitsOptions,a)||(!(a in s)||u!==s[a])&&(s[a]=u,i=!0)}if(o){const a=le(n),u=l||me;for(let c=0;c<o.length;c++){const f=o[c];n[f]=xr(r,a,f,u[f],e,!ue(u,f))}}return i}function xr(e,t,n,s,r,o){const i=e[n];if(i!=null){const l=ue(i,"default");if(l&&s===void 0){const a=i.default;if(i.type!==Function&&!i.skipFactory&&X(a)){const{propsDefaults:u}=r;if(n in u)s=u[n];else{const c=Zn(r);s=u[n]=a.call(null,t),c()}}else s=a;r.ce&&r.ce._setProp(n,s)}i[0]&&(o&&!l?s=!1:i[1]&&(s===""||s===Bt(n))&&(s=!0))}return s}const Xc=new WeakMap;function Cl(e,t,n=!1){const s=n?Xc:t.propsCache,r=s.get(e);if(r)return r;const o=e.props,i={},l=[];let a=!1;if(!X(e)){const c=f=>{a=!0;const[d,p]=Cl(f,t,!0);Ae(i,d),p&&l.push(...p)};!n&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!o&&!a)return ye(e)&&s.set(e,fn),fn;if(G(o))for(let c=0;c<o.length;c++){const f=nt(o[c]);To(f)&&(i[f]=me)}else if(o)for(const c in o){const f=nt(c);if(To(f)){const d=o[c],p=i[f]=G(d)||X(d)?{type:d}:Ae({},d),v=p.type;let y=!1,S=!0;if(G(v))for(let R=0;R<v.length;++R){const C=v[R],D=X(C)&&C.name;if(D==="Boolean"){y=!0;break}else D==="String"&&(S=!1)}else y=X(v)&&v.name==="Boolean";p[0]=y,p[1]=S,(y||ue(p,"default"))&&l.push(f)}}const u=[i,l];return ye(e)&&s.set(e,u),u}function To(e){return e[0]!=="$"&&!Dn(e)}const Al=e=>e[0]==="_"||e==="$stable",no=e=>G(e)?e.map(gt):[gt(e)],Zc=(e,t,n)=>{if(t._n)return t;const s=ul((...r)=>no(t(...r)),n);return s._c=!1,s},Ol=(e,t,n)=>{const s=e._ctx;for(const r in e){if(Al(r))continue;const o=e[r];if(X(o))t[r]=Zc(r,o,s);else if(o!=null){const i=no(o);t[r]=()=>i}}},Pl=(e,t)=>{const n=no(t);e.slots.default=()=>n},Dl=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},eu=(e,t,n)=>{const s=e.slots=El();if(e.vnode.shapeFlag&32){const r=t._;r?(Dl(s,t,n),n&&Li(s,"_",r,!0)):Ol(t,s)}else t&&Pl(e,t)},tu=(e,t,n)=>{const{vnode:s,slots:r}=e;let o=!0,i=me;if(s.shapeFlag&32){const l=t._;l?n&&l===1?o=!1:Dl(r,t,n):(o=!t.$stable,Ol(t,r)),i=t}else t&&(Pl(e,t),i={default:1});if(o)for(const l in r)!Al(l)&&i[l]==null&&delete r[l]},Ge=mu;function nu(e){return su(e)}function su(e,t){const n=Os();n.__VUE__=!0;const{insert:s,remove:r,patchProp:o,createElement:i,createText:l,createComment:a,setText:u,setElementText:c,parentNode:f,nextSibling:d,setScopeId:p=vt,insertStaticContent:v}=e,y=(h,m,b,T=null,x=null,E=null,k=void 0,L=null,$=!!m.dynamicChildren)=>{if(h===m)return;h&&!Yt(h,m)&&(T=w(h),_e(h,x,E,!0),h=null),m.patchFlag===-2&&($=!1,m.dynamicChildren=null);const{type:A,ref:W,shapeFlag:F}=m;switch(A){case Is:S(h,m,b,T);break;case Ue:R(h,m,b,T);break;case tr:h==null&&C(m,b,T,k);break;case Se:H(h,m,b,T,x,E,k,L,$);break;default:F&1?I(h,m,b,T,x,E,k,L,$):F&6?oe(h,m,b,T,x,E,k,L,$):(F&64||F&128)&&A.process(h,m,b,T,x,E,k,L,$,q)}W!=null&&x&&gs(W,h&&h.ref,E,m||h,!m)},S=(h,m,b,T)=>{if(h==null)s(m.el=l(m.children),b,T);else{const x=m.el=h.el;m.children!==h.children&&u(x,m.children)}},R=(h,m,b,T)=>{h==null?s(m.el=a(m.children||""),b,T):m.el=h.el},C=(h,m,b,T)=>{[h.el,h.anchor]=v(h.children,m,b,T,h.el,h.anchor)},D=({el:h,anchor:m},b,T)=>{let x;for(;h&&h!==m;)x=d(h),s(h,b,T),h=x;s(m,b,T)},O=({el:h,anchor:m})=>{let b;for(;h&&h!==m;)b=d(h),r(h),h=b;r(m)},I=(h,m,b,T,x,E,k,L,$)=>{m.type==="svg"?k="svg":m.type==="math"&&(k="mathml"),h==null?M(m,b,T,x,E,k,L,$):V(h,m,x,E,k,L,$)},M=(h,m,b,T,x,E,k,L)=>{let $,A;const{props:W,shapeFlag:F,transition:K,dirs:J}=h;if($=h.el=i(h.type,E,W&&W.is,W),F&8?c($,h.children):F&16&&U(h.children,$,null,T,x,er(h,E),k,L),J&&qt(h,null,T,"created"),P($,h,h.scopeId,k,T),W){for(const ge in W)ge!=="value"&&!Dn(ge)&&o($,ge,null,W[ge],E,T);"value"in W&&o($,"value",null,W.value,E),(A=W.onVnodeBeforeMount)&&dt(A,T,h)}J&&qt(h,null,T,"beforeMount");const re=ru(x,K);re&&K.beforeEnter($),s($,m,b),((A=W&&W.onVnodeMounted)||re||J)&&Ge(()=>{A&&dt(A,T,h),re&&K.enter($),J&&qt(h,null,T,"mounted")},x)},P=(h,m,b,T,x)=>{if(b&&p(h,b),T)for(let E=0;E<T.length;E++)p(h,T[E]);if(x){let E=x.subTree;if(m===E||Nl(E.type)&&(E.ssContent===m||E.ssFallback===m)){const k=x.vnode;P(h,k,k.scopeId,k.slotScopeIds,x.parent)}}},U=(h,m,b,T,x,E,k,L,$=0)=>{for(let A=$;A<h.length;A++){const W=h[A]=L?Lt(h[A]):gt(h[A]);y(null,W,m,b,T,x,E,k,L)}},V=(h,m,b,T,x,E,k)=>{const L=m.el=h.el;let{patchFlag:$,dynamicChildren:A,dirs:W}=m;$|=h.patchFlag&16;const F=h.props||me,K=m.props||me;let J;if(b&&zt(b,!1),(J=K.onVnodeBeforeUpdate)&&dt(J,b,m,h),W&&qt(m,h,b,"beforeUpdate"),b&&zt(b,!0),(F.innerHTML&&K.innerHTML==null||F.textContent&&K.textContent==null)&&c(L,""),A?se(h.dynamicChildren,A,L,b,T,er(m,x),E):k||ie(h,m,L,null,b,T,er(m,x),E,!1),$>0){if($&16)pe(L,F,K,b,x);else if($&2&&F.class!==K.class&&o(L,"class",null,K.class,x),$&4&&o(L,"style",F.style,K.style,x),$&8){const re=m.dynamicProps;for(let ge=0;ge<re.length;ge++){const de=re[ge],Ke=F[de],He=K[de];(He!==Ke||de==="value")&&o(L,de,Ke,He,x,b)}}$&1&&h.children!==m.children&&c(L,m.children)}else!k&&A==null&&pe(L,F,K,b,x);((J=K.onVnodeUpdated)||W)&&Ge(()=>{J&&dt(J,b,m,h),W&&qt(m,h,b,"updated")},T)},se=(h,m,b,T,x,E,k)=>{for(let L=0;L<m.length;L++){const $=h[L],A=m[L],W=$.el&&($.type===Se||!Yt($,A)||$.shapeFlag&70)?f($.el):b;y($,A,W,null,T,x,E,k,!0)}},pe=(h,m,b,T,x)=>{if(m!==b){if(m!==me)for(const E in m)!Dn(E)&&!(E in b)&&o(h,E,m[E],null,x,T);for(const E in b){if(Dn(E))continue;const k=b[E],L=m[E];k!==L&&E!=="value"&&o(h,E,L,k,x,T)}"value"in b&&o(h,"value",m.value,b.value,x)}},H=(h,m,b,T,x,E,k,L,$)=>{const A=m.el=h?h.el:l(""),W=m.anchor=h?h.anchor:l("");let{patchFlag:F,dynamicChildren:K,slotScopeIds:J}=m;J&&(L=L?L.concat(J):J),h==null?(s(A,b,T),s(W,b,T),U(m.children||[],b,W,x,E,k,L,$)):F>0&&F&64&&K&&h.dynamicChildren?(se(h.dynamicChildren,K,b,x,E,k,L),(m.key!=null||x&&m===x.subTree)&&Ml(h,m,!0)):ie(h,m,b,W,x,E,k,L,$)},oe=(h,m,b,T,x,E,k,L,$)=>{m.slotScopeIds=L,h==null?m.shapeFlag&512?x.ctx.activate(m,b,T,k,$):Ee(m,b,T,x,E,k,$):Ie(h,m,$)},Ee=(h,m,b,T,x,E,k)=>{const L=h.component=wu(h,T,x);if(Ms(h)&&(L.ctx.renderer=q),Eu(L,!1,k),L.asyncDep){if(x&&x.registerDep(L,fe,k),!h.el){const $=L.subTree=Re(Ue);R(null,$,m,b)}}else fe(L,h,m,b,x,E,k)},Ie=(h,m,b)=>{const T=m.component=h.component;if(pu(h,m,b))if(T.asyncDep&&!T.asyncResolved){ae(T,m,b);return}else T.next=m,T.update();else m.el=h.el,T.vnode=m},fe=(h,m,b,T,x,E,k)=>{const L=()=>{if(h.isMounted){let{next:F,bu:K,u:J,parent:re,vnode:ge}=h;{const ut=$l(h);if(ut){F&&(F.el=ge.el,ae(h,F,k)),ut.asyncDep.then(()=>{h.isUnmounted||L()});return}}let de=F,Ke;zt(h,!1),F?(F.el=ge.el,ae(h,F,k)):F=ge,K&&ls(K),(Ke=F.props&&F.props.onVnodeBeforeUpdate)&&dt(Ke,re,F,ge),zt(h,!0);const He=xo(h),ct=h.subTree;h.subTree=He,y(ct,He,f(ct.el),w(ct),h,x,E),F.el=He.el,de===null&&hu(h,He.el),J&&Ge(J,x),(Ke=F.props&&F.props.onVnodeUpdated)&&Ge(()=>dt(Ke,re,F,ge),x)}else{let F;const{el:K,props:J}=m,{bm:re,m:ge,parent:de,root:Ke,type:He}=h,ct=Ln(m);zt(h,!1),re&&ls(re),!ct&&(F=J&&J.onVnodeBeforeMount)&&dt(F,de,m),zt(h,!0);{Ke.ce&&Ke.ce._injectChildStyle(He);const ut=h.subTree=xo(h);y(null,ut,b,T,h,x,E),m.el=ut.el}if(ge&&Ge(ge,x),!ct&&(F=J&&J.onVnodeMounted)){const ut=m;Ge(()=>dt(F,de,ut),x)}(m.shapeFlag&256||de&&Ln(de.vnode)&&de.vnode.shapeFlag&256)&&h.a&&Ge(h.a,x),h.isMounted=!0,m=b=T=null}};h.scope.on();const $=h.effect=new Fi(L);h.scope.off();const A=h.update=$.run.bind($),W=h.job=$.runIfDirty.bind($);W.i=h,W.id=h.uid,$.scheduler=()=>to(W),zt(h,!0),A()},ae=(h,m,b)=>{m.component=h;const T=h.vnode.props;h.vnode=m,h.next=null,Qc(h,m.props,T,b),tu(h,m.children,b),Ut(),go(h),Vt()},ie=(h,m,b,T,x,E,k,L,$=!1)=>{const A=h&&h.children,W=h?h.shapeFlag:0,F=m.children,{patchFlag:K,shapeFlag:J}=m;if(K>0){if(K&128){at(A,F,b,T,x,E,k,L,$);return}else if(K&256){st(A,F,b,T,x,E,k,L,$);return}}J&8?(W&16&&xe(A,x,E),F!==A&&c(b,F)):W&16?J&16?at(A,F,b,T,x,E,k,L,$):xe(A,x,E,!0):(W&8&&c(b,""),J&16&&U(F,b,T,x,E,k,L,$))},st=(h,m,b,T,x,E,k,L,$)=>{h=h||fn,m=m||fn;const A=h.length,W=m.length,F=Math.min(A,W);let K;for(K=0;K<F;K++){const J=m[K]=$?Lt(m[K]):gt(m[K]);y(h[K],J,b,null,x,E,k,L,$)}A>W?xe(h,x,E,!0,!1,F):U(m,b,T,x,E,k,L,$,F)},at=(h,m,b,T,x,E,k,L,$)=>{let A=0;const W=m.length;let F=h.length-1,K=W-1;for(;A<=F&&A<=K;){const J=h[A],re=m[A]=$?Lt(m[A]):gt(m[A]);if(Yt(J,re))y(J,re,b,null,x,E,k,L,$);else break;A++}for(;A<=F&&A<=K;){const J=h[F],re=m[K]=$?Lt(m[K]):gt(m[K]);if(Yt(J,re))y(J,re,b,null,x,E,k,L,$);else break;F--,K--}if(A>F){if(A<=K){const J=K+1,re=J<W?m[J].el:T;for(;A<=K;)y(null,m[A]=$?Lt(m[A]):gt(m[A]),b,re,x,E,k,L,$),A++}}else if(A>K)for(;A<=F;)_e(h[A],x,E,!0),A++;else{const J=A,re=A,ge=new Map;for(A=re;A<=K;A++){const We=m[A]=$?Lt(m[A]):gt(m[A]);We.key!=null&&ge.set(We.key,A)}let de,Ke=0;const He=K-re+1;let ct=!1,ut=0;const Sn=new Array(He);for(A=0;A<He;A++)Sn[A]=0;for(A=J;A<=F;A++){const We=h[A];if(Ke>=He){_e(We,x,E,!0);continue}let ft;if(We.key!=null)ft=ge.get(We.key);else for(de=re;de<=K;de++)if(Sn[de-re]===0&&Yt(We,m[de])){ft=de;break}ft===void 0?_e(We,x,E,!0):(Sn[ft-re]=A+1,ft>=ut?ut=ft:ct=!0,y(We,m[ft],b,null,x,E,k,L,$),Ke++)}const uo=ct?ou(Sn):fn;for(de=uo.length-1,A=He-1;A>=0;A--){const We=re+A,ft=m[We],fo=We+1<W?m[We+1].el:T;Sn[A]===0?y(null,ft,b,fo,x,E,k,L,$):ct&&(de<0||A!==uo[de]?ke(ft,b,fo,2):de--)}}},ke=(h,m,b,T,x=null)=>{const{el:E,type:k,transition:L,children:$,shapeFlag:A}=h;if(A&6){ke(h.component.subTree,m,b,T);return}if(A&128){h.suspense.move(m,b,T);return}if(A&64){k.move(h,m,b,q);return}if(k===Se){s(E,m,b);for(let F=0;F<$.length;F++)ke($[F],m,b,T);s(h.anchor,m,b);return}if(k===tr){D(h,m,b);return}if(T!==2&&A&1&&L)if(T===0)L.beforeEnter(E),s(E,m,b),Ge(()=>L.enter(E),x);else{const{leave:F,delayLeave:K,afterLeave:J}=L,re=()=>s(E,m,b),ge=()=>{F(E,()=>{re(),J&&J()})};K?K(E,re,ge):ge()}else s(E,m,b)},_e=(h,m,b,T=!1,x=!1)=>{const{type:E,props:k,ref:L,children:$,dynamicChildren:A,shapeFlag:W,patchFlag:F,dirs:K,cacheIndex:J}=h;if(F===-2&&(x=!1),L!=null&&gs(L,null,b,h,!0),J!=null&&(m.renderCache[J]=void 0),W&256){m.ctx.deactivate(h);return}const re=W&1&&K,ge=!Ln(h);let de;if(ge&&(de=k&&k.onVnodeBeforeUnmount)&&dt(de,m,h),W&6)be(h.component,b,T);else{if(W&128){h.suspense.unmount(b,T);return}re&&qt(h,null,m,"beforeUnmount"),W&64?h.type.remove(h,m,b,q,T):A&&!A.hasOnce&&(E!==Se||F>0&&F&64)?xe(A,m,b,!1,!0):(E===Se&&F&384||!x&&W&16)&&xe($,m,b),T&&Q(h)}(ge&&(de=k&&k.onVnodeUnmounted)||re)&&Ge(()=>{de&&dt(de,m,h),re&&qt(h,null,m,"unmounted")},b)},Q=h=>{const{type:m,el:b,anchor:T,transition:x}=h;if(m===Se){te(b,T);return}if(m===tr){O(h);return}const E=()=>{r(b),x&&!x.persisted&&x.afterLeave&&x.afterLeave()};if(h.shapeFlag&1&&x&&!x.persisted){const{leave:k,delayLeave:L}=x,$=()=>k(b,E);L?L(h.el,E,$):$()}else E()},te=(h,m)=>{let b;for(;h!==m;)b=d(h),r(h),h=b;r(m)},be=(h,m,b)=>{const{bum:T,scope:x,job:E,subTree:k,um:L,m:$,a:A}=h;Eo($),Eo(A),T&&ls(T),x.stop(),E&&(E.flags|=8,_e(k,h,m,b)),L&&Ge(L,m),Ge(()=>{h.isUnmounted=!0},m),m&&m.pendingBranch&&!m.isUnmounted&&h.asyncDep&&!h.asyncResolved&&h.suspenseId===m.pendingId&&(m.deps--,m.deps===0&&m.resolve())},xe=(h,m,b,T=!1,x=!1,E=0)=>{for(let k=E;k<h.length;k++)_e(h[k],m,b,T,x)},w=h=>{if(h.shapeFlag&6)return w(h.component.subTree);if(h.shapeFlag&128)return h.suspense.next();const m=d(h.anchor||h.el),b=m&&m[Ec];return b?d(b):m};let j=!1;const N=(h,m,b)=>{h==null?m._vnode&&_e(m._vnode,null,null,!0):y(m._vnode||null,h,m,null,null,null,b),m._vnode=h,j||(j=!0,go(),ll(),j=!1)},q={p:y,um:_e,m:ke,r:Q,mt:Ee,mc:U,pc:ie,pbc:se,n:w,o:e};return{render:N,hydrate:void 0,createApp:Jc(N)}}function er({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function zt({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function ru(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Ml(e,t,n=!1){const s=e.children,r=t.children;if(G(s)&&G(r))for(let o=0;o<s.length;o++){const i=s[o];let l=r[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[o]=Lt(r[o]),l.el=i.el),!n&&l.patchFlag!==-2&&Ml(i,l)),l.type===Is&&(l.el=i.el)}}function ou(e){const t=e.slice(),n=[0];let s,r,o,i,l;const a=e.length;for(s=0;s<a;s++){const u=e[s];if(u!==0){if(r=n[n.length-1],e[r]<u){t[s]=r,n.push(s);continue}for(o=0,i=n.length-1;o<i;)l=o+i>>1,e[n[l]]<u?o=l+1:i=l;u<e[n[o]]&&(o>0&&(t[s]=n[o-1]),n[o]=s)}}for(o=n.length,i=n[o-1];o-- >0;)n[o]=i,i=t[i];return n}function $l(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:$l(t)}function Eo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const iu=Symbol.for("v-scx"),lu=()=>tt(iu);function tn(e,t,n){return Ll(e,t,n)}function Ll(e,t,n=me){const{immediate:s,deep:r,flush:o,once:i}=n,l=Ae({},n),a=t&&s||!t&&o!=="post";let u;if(Kn){if(o==="sync"){const p=lu();u=p.__watcherHandles||(p.__watcherHandles=[])}else if(!a){const p=()=>{};return p.stop=vt,p.resume=vt,p.pause=vt,p}}const c=Oe;l.call=(p,v,y)=>ot(p,c,v,y);let f=!1;o==="post"?l.scheduler=p=>{Ge(p,c&&c.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(p,v)=>{v?p():to(p)}),l.augmentJob=p=>{t&&(p.flags|=4),f&&(p.flags|=2,c&&(p.id=c.uid,p.i=c))};const d=_c(e,t,l);return Kn&&(u?u.push(d):a&&d()),d}function au(e,t,n){const s=this.proxy,r=we(e)?e.includes(".")?Il(s,e):()=>s[e]:e.bind(s,s);let o;X(t)?o=t:(o=t.handler,n=t);const i=Zn(this),l=Ll(r,o.bind(s),n);return i(),l}function Il(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}const cu=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${nt(t)}Modifiers`]||e[`${Bt(t)}Modifiers`];function uu(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||me;let r=n;const o=t.startsWith("update:"),i=o&&cu(s,t.slice(7));i&&(i.trim&&(r=n.map(c=>we(c)?c.trim():c)),i.number&&(r=n.map(mr)));let l,a=s[l=Ws(t)]||s[l=Ws(nt(t))];!a&&o&&(a=s[l=Ws(Bt(t))]),a&&ot(a,e,6,r);const u=s[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,ot(u,e,6,r)}}function kl(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const o=e.emits;let i={},l=!1;if(!X(e)){const a=u=>{const c=kl(u,t,!0);c&&(l=!0,Ae(i,c))};!n&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!o&&!l?(ye(e)&&s.set(e,null),null):(G(o)?o.forEach(a=>i[a]=null):Ae(i,o),ye(e)&&s.set(e,i),i)}function Ls(e,t){return!e||!xs(t)?!1:(t=t.slice(2).replace(/Once$/,""),ue(e,t[0].toLowerCase()+t.slice(1))||ue(e,Bt(t))||ue(e,t))}function xo(e){const{type:t,vnode:n,proxy:s,withProxy:r,propsOptions:[o],slots:i,attrs:l,emit:a,render:u,renderCache:c,props:f,data:d,setupState:p,ctx:v,inheritAttrs:y}=e,S=ms(e);let R,C;try{if(n.shapeFlag&4){const O=r||s,I=O;R=gt(u.call(I,O,c,f,p,d,v)),C=l}else{const O=t;R=gt(O.length>1?O(f,{attrs:l,slots:i,emit:a}):O(f,null)),C=t.props?l:fu(l)}}catch(O){kn.length=0,Ds(O,e,1),R=Re(Ue)}let D=R;if(C&&y!==!1){const O=Object.keys(C),{shapeFlag:I}=D;O.length&&I&7&&(o&&O.some(qr)&&(C=du(C,o)),D=Ft(D,C,!1,!0))}return n.dirs&&(D=Ft(D,null,!1,!0),D.dirs=D.dirs?D.dirs.concat(n.dirs):n.dirs),n.transition&&qn(D,n.transition),R=D,ms(S),R}const fu=e=>{let t;for(const n in e)(n==="class"||n==="style"||xs(n))&&((t||(t={}))[n]=e[n]);return t},du=(e,t)=>{const n={};for(const s in e)(!qr(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function pu(e,t,n){const{props:s,children:r,component:o}=e,{props:i,children:l,patchFlag:a}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&a>=0){if(a&1024)return!0;if(a&16)return s?Ro(s,i,u):!!i;if(a&8){const c=t.dynamicProps;for(let f=0;f<c.length;f++){const d=c[f];if(i[d]!==s[d]&&!Ls(u,d))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===i?!1:s?i?Ro(s,i,u):!0:!!i;return!1}function Ro(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const o=s[r];if(t[o]!==e[o]&&!Ls(n,o))return!0}return!1}function hu({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Nl=e=>e.__isSuspense;function mu(e,t){t&&t.pendingBranch?G(e)?t.effects.push(...e):t.effects.push(e):Tc(e)}const Se=Symbol.for("v-fgt"),Is=Symbol.for("v-txt"),Ue=Symbol.for("v-cmt"),tr=Symbol.for("v-stc"),kn=[];let Qe=null;function Z(e=!1){kn.push(Qe=e?null:[])}function gu(){kn.pop(),Qe=kn[kn.length-1]||null}let zn=1;function Co(e,t=!1){zn+=e,e<0&&Qe&&t&&(Qe.hasOnce=!0)}function jl(e){return e.dynamicChildren=zn>0?Qe||fn:null,gu(),zn>0&&Qe&&Qe.push(e),e}function ne(e,t,n,s,r,o){return jl(g(e,t,n,s,r,o,!0))}function Fl(e,t,n,s,r){return jl(Re(e,t,n,s,r,!0))}function ys(e){return e?e.__v_isVNode===!0:!1}function Yt(e,t){return e.type===t.type&&e.key===t.key}const Hl=({key:e})=>e??null,as=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?we(e)||Le(e)||X(e)?{i:Ve,r:e,k:t,f:!!n}:e:null);function g(e,t=null,n=null,s=0,r=null,o=e===Se?0:1,i=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hl(t),ref:t&&as(t),scopeId:cl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Ve};return l?(so(a,n),o&128&&e.normalize(a)):n&&(a.shapeFlag|=we(n)?8:16),zn>0&&!i&&Qe&&(a.patchFlag>0||o&6)&&a.patchFlag!==32&&Qe.push(a),a}const Re=vu;function vu(e,t=null,n=null,s=0,r=null,o=!1){if((!e||e===Hc)&&(e=Ue),ys(e)){const l=Ft(e,t,!0);return n&&so(l,n),zn>0&&!o&&Qe&&(l.shapeFlag&6?Qe[Qe.indexOf(e)]=l:Qe.push(l)),l.patchFlag=-2,l}if(Ou(e)&&(e=e.__vccOpts),t){t=yu(t);let{class:l,style:a}=t;l&&!we(l)&&(t.class=je(l)),ye(a)&&(eo(a)&&!G(a)&&(a=Ae({},a)),t.style=Wr(a))}const i=we(e)?1:Nl(e)?128:fl(e)?64:ye(e)?4:X(e)?2:0;return g(e,t,n,s,r,i,o,!0)}function yu(e){return e?eo(e)||xl(e)?Ae({},e):e:null}function Ft(e,t,n=!1,s=!1){const{props:r,ref:o,patchFlag:i,children:l,transition:a}=e,u=t?bu(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Hl(u),ref:t&&t.ref?n&&o?G(o)?o.concat(as(t)):[o,as(t)]:as(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Se?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ft(e.ssContent),ssFallback:e.ssFallback&&Ft(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&s&&qn(c,a.clone(c)),c}function Rr(e=" ",t=0){return Re(Is,null,e,t)}function Rt(e="",t=!1){return t?(Z(),Fl(Ue,null,e)):Re(Ue,null,e)}function gt(e){return e==null||typeof e=="boolean"?Re(Ue):G(e)?Re(Se,null,e.slice()):ys(e)?Lt(e):Re(Is,null,String(e))}function Lt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ft(e)}function so(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(G(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),so(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!xl(t)?t._ctx=Ve:r===3&&Ve&&(Ve.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else X(t)?(t={default:t,_ctx:Ve},n=32):(t=String(t),s&64?(n=16,t=[Rr(t)]):n=8);e.children=t,e.shapeFlag|=n}function bu(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=je([t.class,s.class]));else if(r==="style")t.style=Wr([t.style,s.style]);else if(xs(r)){const o=t[r],i=s[r];i&&o!==i&&!(G(o)&&o.includes(i))&&(t[r]=o?[].concat(o,i):i)}else r!==""&&(t[r]=s[r])}return t}function dt(e,t,n,s=null){ot(e,t,7,[n,s])}const _u=wl();let Su=0;function wu(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||_u,o={uid:Su++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ji(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Cl(s,r),emitsOptions:kl(s,r),emit:null,emitted:null,propsDefaults:me,inheritAttrs:s.inheritAttrs,ctx:me,data:me,props:me,attrs:me,slots:me,refs:me,setupState:me,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=uu.bind(null,o),e.ce&&e.ce(o),o}let Oe=null;const Tu=()=>Oe||Ve;let bs,Cr;{const e=Os(),t=(n,s)=>{let r;return(r=e[n])||(r=e[n]=[]),r.push(s),o=>{r.length>1?r.forEach(i=>i(o)):r[0](o)}};bs=t("__VUE_INSTANCE_SETTERS__",n=>Oe=n),Cr=t("__VUE_SSR_SETTERS__",n=>Kn=n)}const Zn=e=>{const t=Oe;return bs(e),e.scope.on(),()=>{e.scope.off(),bs(t)}},Ao=()=>{Oe&&Oe.scope.off(),bs(null)};function Bl(e){return e.vnode.shapeFlag&4}let Kn=!1;function Eu(e,t=!1,n=!1){t&&Cr(t);const{props:s,children:r}=e.vnode,o=Bl(e);Yc(e,s,o,t),eu(e,r,n);const i=o?xu(e,t):void 0;return t&&Cr(!1),i}function xu(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Uc);const{setup:s}=n;if(s){Ut();const r=e.setupContext=s.length>1?Cu(e):null,o=Zn(e),i=Qn(s,e,0,[e.props,r]),l=Di(i);if(Vt(),o(),(l||e.sp)&&!Ln(e)&&vl(e),l){if(i.then(Ao,Ao),t)return i.then(a=>{Oo(e,a)}).catch(a=>{Ds(a,e,0)});e.asyncDep=i}else Oo(e,i)}else Ul(e)}function Oo(e,t,n){X(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ye(t)&&(e.setupState=sl(t)),Ul(e)}function Ul(e,t,n){const s=e.type;e.render||(e.render=s.render||vt);{const r=Zn(e);Ut();try{Vc(e)}finally{Vt(),r()}}}const Ru={get(e,t){return Pe(e,"get",""),e[t]}};function Cu(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,Ru),slots:e.slots,emit:e.emit,expose:t}}function ks(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(sl(tl(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in In)return In[n](e)},has(t,n){return n in t||n in In}})):e.proxy}function Au(e,t=!0){return X(e)?e.displayName||e.name:e.name||t&&e.__name}function Ou(e){return X(e)&&"__vccOpts"in e}const Ye=(e,t)=>yc(e,t,Kn);function ro(e,t,n){const s=arguments.length;return s===2?ye(t)&&!G(t)?ys(t)?Re(e,null,[t]):Re(e,t):Re(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&ys(n)&&(n=[n]),Re(e,t,n))}const Pu="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ar;const Po=typeof window<"u"&&window.trustedTypes;if(Po)try{Ar=Po.createPolicy("vue",{createHTML:e=>e})}catch{}const Vl=Ar?e=>Ar.createHTML(e):e=>e,Du="http://www.w3.org/2000/svg",Mu="http://www.w3.org/1998/Math/MathML",St=typeof document<"u"?document:null,Do=St&&St.createElement("template"),$u={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t==="svg"?St.createElementNS(Du,e):t==="mathml"?St.createElementNS(Mu,e):n?St.createElement(e,{is:n}):St.createElement(e);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>St.createTextNode(e),createComment:e=>St.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>St.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,o){const i=n?n.previousSibling:t.lastChild;if(r&&(r===o||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===o||!(r=r.nextSibling)););else{Do.innerHTML=Vl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const l=Do.content;if(s==="svg"||s==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,n)}return[i?i.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},Pt="transition",En="animation",Wn=Symbol("_vtc"),ql={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Lu=Ae({},dl,ql),Iu=e=>(e.displayName="Transition",e.props=Lu,e),ku=Iu((e,{slots:t})=>ro(Cc,Nu(e),t)),Kt=(e,t=[])=>{G(e)?e.forEach(n=>n(...t)):e&&e(...t)},Mo=e=>e?G(e)?e.some(t=>t.length>1):e.length>1:!1;function Nu(e){const t={};for(const H in e)H in ql||(t[H]=e[H]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:o=`${n}-enter-from`,enterActiveClass:i=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:a=o,appearActiveClass:u=i,appearToClass:c=l,leaveFromClass:f=`${n}-leave-from`,leaveActiveClass:d=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,v=ju(r),y=v&&v[0],S=v&&v[1],{onBeforeEnter:R,onEnter:C,onEnterCancelled:D,onLeave:O,onLeaveCancelled:I,onBeforeAppear:M=R,onAppear:P=C,onAppearCancelled:U=D}=t,V=(H,oe,Ee,Ie)=>{H._enterCancelled=Ie,Wt(H,oe?c:l),Wt(H,oe?u:i),Ee&&Ee()},se=(H,oe)=>{H._isLeaving=!1,Wt(H,f),Wt(H,p),Wt(H,d),oe&&oe()},pe=H=>(oe,Ee)=>{const Ie=H?P:C,fe=()=>V(oe,H,Ee);Kt(Ie,[oe,fe]),$o(()=>{Wt(oe,H?a:o),bt(oe,H?c:l),Mo(Ie)||Lo(oe,s,y,fe)})};return Ae(t,{onBeforeEnter(H){Kt(R,[H]),bt(H,o),bt(H,i)},onBeforeAppear(H){Kt(M,[H]),bt(H,a),bt(H,u)},onEnter:pe(!1),onAppear:pe(!0),onLeave(H,oe){H._isLeaving=!0;const Ee=()=>se(H,oe);bt(H,f),H._enterCancelled?(bt(H,d),No()):(No(),bt(H,d)),$o(()=>{H._isLeaving&&(Wt(H,f),bt(H,p),Mo(O)||Lo(H,s,S,Ee))}),Kt(O,[H,Ee])},onEnterCancelled(H){V(H,!1,void 0,!0),Kt(D,[H])},onAppearCancelled(H){V(H,!0,void 0,!0),Kt(U,[H])},onLeaveCancelled(H){se(H),Kt(I,[H])}})}function ju(e){if(e==null)return null;if(ye(e))return[nr(e.enter),nr(e.leave)];{const t=nr(e);return[t,t]}}function nr(e){return Ba(e)}function bt(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e[Wn]||(e[Wn]=new Set)).add(t)}function Wt(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const n=e[Wn];n&&(n.delete(t),n.size||(e[Wn]=void 0))}function $o(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let Fu=0;function Lo(e,t,n,s){const r=e._endId=++Fu,o=()=>{r===e._endId&&s()};if(n!=null)return setTimeout(o,n);const{type:i,timeout:l,propCount:a}=Hu(e,t);if(!i)return s();const u=i+"end";let c=0;const f=()=>{e.removeEventListener(u,d),o()},d=p=>{p.target===e&&++c>=a&&f()};setTimeout(()=>{c<a&&f()},l+1),e.addEventListener(u,d)}function Hu(e,t){const n=window.getComputedStyle(e),s=v=>(n[v]||"").split(", "),r=s(`${Pt}Delay`),o=s(`${Pt}Duration`),i=Io(r,o),l=s(`${En}Delay`),a=s(`${En}Duration`),u=Io(l,a);let c=null,f=0,d=0;t===Pt?i>0&&(c=Pt,f=i,d=o.length):t===En?u>0&&(c=En,f=u,d=a.length):(f=Math.max(i,u),c=f>0?i>u?Pt:En:null,d=c?c===Pt?o.length:a.length:0);const p=c===Pt&&/\b(transform|all)(,|$)/.test(s(`${Pt}Property`).toString());return{type:c,timeout:f,propCount:d,hasTransform:p}}function Io(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>ko(n)+ko(e[s])))}function ko(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function No(){return document.body.offsetHeight}function Bu(e,t,n){const s=e[Wn];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const _s=Symbol("_vod"),zl=Symbol("_vsh"),sr={beforeMount(e,{value:t},{transition:n}){e[_s]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):xn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),xn(e,!0),s.enter(e)):s.leave(e,()=>{xn(e,!1)}):xn(e,t))},beforeUnmount(e,{value:t}){xn(e,t)}};function xn(e,t){e.style.display=t?e[_s]:"none",e[zl]=!t}const Uu=Symbol(""),Vu=/(^|;)\s*display\s*:/;function qu(e,t,n){const s=e.style,r=we(n);let o=!1;if(n&&!r){if(t)if(we(t))for(const i of t.split(";")){const l=i.slice(0,i.indexOf(":")).trim();n[l]==null&&cs(s,l,"")}else for(const i in t)n[i]==null&&cs(s,i,"");for(const i in n)i==="display"&&(o=!0),cs(s,i,n[i])}else if(r){if(t!==n){const i=s[Uu];i&&(n+=";"+i),s.cssText=n,o=Vu.test(n)}}else t&&e.removeAttribute("style");_s in e&&(e[_s]=o?s.display:"",e[zl]&&(s.display="none"))}const jo=/\s*!important$/;function cs(e,t,n){if(G(n))n.forEach(s=>cs(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=zu(e,t);jo.test(n)?e.setProperty(Bt(s),n.replace(jo,""),"important"):e[s]=n}}const Fo=["Webkit","Moz","ms"],rr={};function zu(e,t){const n=rr[t];if(n)return n;let s=nt(t);if(s!=="filter"&&s in e)return rr[t]=s;s=As(s);for(let r=0;r<Fo.length;r++){const o=Fo[r]+s;if(o in e)return rr[t]=o}return t}const Ho="http://www.w3.org/1999/xlink";function Bo(e,t,n,s,r,o=Wa(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Ho,t.slice(6,t.length)):e.setAttributeNS(Ho,t,n):n==null||o&&!Ii(n)?e.removeAttribute(t):e.setAttribute(t,o?"":Ht(n)?String(n):n)}function Uo(e,t,n,s,r){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Vl(n):n);return}const o=e.tagName;if(t==="value"&&o!=="PROGRESS"&&!o.includes("-")){const l=o==="OPTION"?e.getAttribute("value")||"":e.value,a=n==null?e.type==="checkbox"?"on":"":String(n);(l!==a||!("_value"in e))&&(e.value=a),n==null&&e.removeAttribute(t),e._value=n;return}let i=!1;if(n===""||n==null){const l=typeof e[t];l==="boolean"?n=Ii(n):n==null&&l==="string"?(n="",i=!0):l==="number"&&(n=0,i=!0)}try{e[t]=n}catch{}i&&e.removeAttribute(r||t)}function cn(e,t,n,s){e.addEventListener(t,n,s)}function Ku(e,t,n,s){e.removeEventListener(t,n,s)}const Vo=Symbol("_vei");function Wu(e,t,n,s,r=null){const o=e[Vo]||(e[Vo]={}),i=o[t];if(s&&i)i.value=s;else{const[l,a]=Gu(t);if(s){const u=o[t]=Qu(s,r);cn(e,l,u,a)}else i&&(Ku(e,l,i,a),o[t]=void 0)}}const qo=/(?:Once|Passive|Capture)$/;function Gu(e){let t;if(qo.test(e)){t={};let s;for(;s=e.match(qo);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Bt(e.slice(2)),t]}let or=0;const Ju=Promise.resolve(),Yu=()=>or||(Ju.then(()=>or=0),or=Date.now());function Qu(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;ot(Xu(s,n.value),t,5,[s])};return n.value=e,n.attached=Yu(),n}function Xu(e,t){if(G(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const zo=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Zu=(e,t,n,s,r,o)=>{const i=r==="svg";t==="class"?Bu(e,s,i):t==="style"?qu(e,n,s):xs(t)?qr(t)||Wu(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):ef(e,t,s,i))?(Uo(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Bo(e,t,s,i,o,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!we(s))?Uo(e,nt(t),s,o,t):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Bo(e,t,s,i))};function ef(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&zo(t)&&X(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return zo(t)&&we(n)?!1:t in e}const Ko=e=>{const t=e.props["onUpdate:modelValue"]||!1;return G(t)?n=>ls(t,n):t};function tf(e){e.target.composing=!0}function Wo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ir=Symbol("_assign"),On={created(e,{modifiers:{lazy:t,trim:n,number:s}},r){e[ir]=Ko(r);const o=s||r.props&&r.props.type==="number";cn(e,t?"change":"input",i=>{if(i.target.composing)return;let l=e.value;n&&(l=l.trim()),o&&(l=mr(l)),e[ir](l)}),n&&cn(e,"change",()=>{e.value=e.value.trim()}),t||(cn(e,"compositionstart",tf),cn(e,"compositionend",Wo),cn(e,"change",Wo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:r,number:o}},i){if(e[ir]=Ko(i),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?mr(e.value):e.value,a=t??"";l!==a&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||r&&e.value.trim()===a)||(e.value=a))}},nf={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},sf=(e,t)=>{const n=e._withKeys||(e._withKeys={}),s=t.join(".");return n[s]||(n[s]=r=>{if(!("key"in r))return;const o=Bt(r.key);if(t.some(i=>i===o||nf[i]===o))return e(r)})},rf=Ae({patchProp:Zu},$u);let Go;function of(){return Go||(Go=nu(rf))}const lf=(...e)=>{const t=of().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=cf(s);if(!r)return;const o=t._component;!X(o)&&!o.render&&!o.template&&(o.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const i=n(r,!1,af(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),i},t};function af(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function cf(e){return we(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const uf=Symbol();var Jo;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(Jo||(Jo={}));function ff(){const e=Ga(!0),t=e.run(()=>z({}));let n=[],s=[];const r=tl({install(o){r._a=o,o.provide(uf,r),o.config.globalProperties.$pinia=r,s.forEach(i=>n.push(i)),s=[]},use(o){return this._a?n.push(o):s.push(o),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return r}const df={class:"notification-content"},pf={key:0,class:"notification-title"},hf={class:"notification-message"},mf=At({__name:"NotificationPopup",props:{show:{type:Boolean},type:{},title:{},message:{},duration:{},position:{}},emits:["close"],setup(e,{emit:t}){const n=e,s=t,r=z(!1),o=z(null),i={info:"notification-info",success:"notification-success",warning:"notification-warning",error:"notification-error"},l={top:"notification-top",center:"notification-center",bottom:"notification-bottom"},a=Ye(()=>i[n.type||"info"]),u=Ye(()=>l[n.position||"center"]),c=()=>{r.value=!1,setTimeout(()=>{s("close")},300)},f=()=>{o.value&&(clearTimeout(o.value),o.value=null)};tn(()=>n.show,v=>{v?(r.value=!0,n.duration&&n.duration>0&&(f(),o.value=window.setTimeout(()=>{c()},n.duration))):r.value=!1},{immediate:!0});const d=()=>{f()},p=()=>{n.duration&&n.duration>0&&r.value&&(o.value=window.setTimeout(()=>{c()},n.duration))};return Xn(()=>{f()}),(v,y)=>(Z(),Fl(ku,{name:"notification-fade"},{default:ul(()=>[r.value?(Z(),ne("div",{key:0,class:je(["notification-popup",a.value,u.value]),onMouseenter:d,onMouseleave:p},[g("div",df,[v.title?(Z(),ne("div",pf,Y(v.title),1)):Rt("",!0),g("div",hf,Y(v.message),1)]),g("button",{class:"notification-close",onClick:c},"×")],34)):Rt("",!0)]),_:1}))}}),Kl=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Or=Kl(mf,[["__scopeId","data-v-a3353551"]]);function gf(){const e=Ct({show:!1,message:"",type:"info",duration:5e3,position:"top"}),t=l=>{e.show?(e.show=!1,setTimeout(()=>{Object.assign(e,{show:!0,title:l.title||"",message:l.message,type:l.type||"info",duration:l.duration!==void 0?l.duration:5e3,position:l.position||"top"})},300)):Object.assign(e,{show:!0,title:l.title||"",message:l.message,type:l.type||"info",duration:l.duration!==void 0?l.duration:5e3,position:l.position||"top"})};return{notification:e,showNotification:t,hideNotification:()=>{e.show=!1},info:(l,a,u={})=>{t({title:a,message:l,type:"info",...u})},success:(l,a,u={})=>{t({title:a,message:l,type:"success",...u})},warning:(l,a,u={})=>{t({title:a,message:l,type:"warning",...u})},error:(l,a,u={})=>{t({title:a,message:l,type:"error",...u})}}}const Me=Ct({show:!1,title:"",message:"",type:"info",duration:5e3,position:"top"});function Ns(e){Me.show?(Me.show=!1,setTimeout(()=>{Yo(e)},300)):Yo(e)}function Yo(e){Me.title=e.title||"",Me.message=e.message,Me.type=e.type||"info",Me.duration=e.duration!==void 0?e.duration:5e3,Me.position=e.position||"top",Me.show=!0}function Wl(){Me.show=!1}function vf(e,t){Ns({title:t,message:e,type:"info"})}function Pr(e,t){Ns({title:t,message:e,type:"success"})}function Gl(e,t){Ns({title:t,message:e,type:"warning"})}function gn(e,t){Ns({title:t,message:e,type:"error"})}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const un=typeof document<"u";function Jl(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function yf(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Jl(e.default)}const ce=Object.assign;function lr(e,t){const n={};for(const s in t){const r=t[s];n[s]=it(r)?r.map(e):e(r)}return n}const Nn=()=>{},it=Array.isArray,Yl=/#/g,bf=/&/g,_f=/\//g,Sf=/=/g,wf=/\?/g,Ql=/\+/g,Tf=/%5B/g,Ef=/%5D/g,Xl=/%5E/g,xf=/%60/g,Zl=/%7B/g,Rf=/%7C/g,ea=/%7D/g,Cf=/%20/g;function oo(e){return encodeURI(""+e).replace(Rf,"|").replace(Tf,"[").replace(Ef,"]")}function Af(e){return oo(e).replace(Zl,"{").replace(ea,"}").replace(Xl,"^")}function Dr(e){return oo(e).replace(Ql,"%2B").replace(Cf,"+").replace(Yl,"%23").replace(bf,"%26").replace(xf,"`").replace(Zl,"{").replace(ea,"}").replace(Xl,"^")}function Of(e){return Dr(e).replace(Sf,"%3D")}function Pf(e){return oo(e).replace(Yl,"%23").replace(wf,"%3F")}function Df(e){return e==null?"":Pf(e).replace(_f,"%2F")}function Gn(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Mf=/\/$/,$f=e=>e.replace(Mf,"");function ar(e,t,n="/"){let s,r={},o="",i="";const l=t.indexOf("#");let a=t.indexOf("?");return l<a&&l>=0&&(a=-1),a>-1&&(s=t.slice(0,a),o=t.slice(a+1,l>-1?l:t.length),r=e(o)),l>-1&&(s=s||t.slice(0,l),i=t.slice(l,t.length)),s=Nf(s??t,n),{fullPath:s+(o&&"?")+o+i,path:s,query:r,hash:Gn(i)}}function Lf(e,t){const n=t.query?e(t.query):"";return t.path+(n&&"?")+n+(t.hash||"")}function Qo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function If(e,t,n){const s=t.matched.length-1,r=n.matched.length-1;return s>-1&&s===r&&vn(t.matched[s],n.matched[r])&&ta(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function vn(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function ta(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!kf(e[n],t[n]))return!1;return!0}function kf(e,t){return it(e)?Xo(e,t):it(t)?Xo(t,e):e===t}function Xo(e,t){return it(t)?e.length===t.length&&e.every((n,s)=>n===t[s]):e.length===1&&e[0]===t}function Nf(e,t){if(e.startsWith("/"))return e;if(!e)return t;const n=t.split("/"),s=e.split("/"),r=s[s.length-1];(r===".."||r===".")&&s.push("");let o=n.length-1,i,l;for(i=0;i<s.length;i++)if(l=s[i],l!==".")if(l==="..")o>1&&o--;else break;return n.slice(0,o).join("/")+"/"+s.slice(i).join("/")}const Dt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Jn;(function(e){e.pop="pop",e.push="push"})(Jn||(Jn={}));var jn;(function(e){e.back="back",e.forward="forward",e.unknown=""})(jn||(jn={}));function jf(e){if(!e)if(un){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),$f(e)}const Ff=/^[^#]+#/;function Hf(e,t){return e.replace(Ff,"#")+t}function Bf(e,t){const n=document.documentElement.getBoundingClientRect(),s=e.getBoundingClientRect();return{behavior:t.behavior,left:s.left-n.left-(t.left||0),top:s.top-n.top-(t.top||0)}}const js=()=>({left:window.scrollX,top:window.scrollY});function Uf(e){let t;if("el"in e){const n=e.el,s=typeof n=="string"&&n.startsWith("#"),r=typeof n=="string"?s?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!r)return;t=Bf(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Zo(e,t){return(history.state?history.state.position-t:-1)+e}const Mr=new Map;function Vf(e,t){Mr.set(e,t)}function qf(e){const t=Mr.get(e);return Mr.delete(e),t}let zf=()=>location.protocol+"//"+location.host;function na(e,t){const{pathname:n,search:s,hash:r}=t,o=e.indexOf("#");if(o>-1){let l=r.includes(e.slice(o))?e.slice(o).length:1,a=r.slice(l);return a[0]!=="/"&&(a="/"+a),Qo(a,"")}return Qo(n,e)+s+r}function Kf(e,t,n,s){let r=[],o=[],i=null;const l=({state:d})=>{const p=na(e,location),v=n.value,y=t.value;let S=0;if(d){if(n.value=p,t.value=d,i&&i===v){i=null;return}S=y?d.position-y.position:0}else s(p);r.forEach(R=>{R(n.value,v,{delta:S,type:Jn.pop,direction:S?S>0?jn.forward:jn.back:jn.unknown})})};function a(){i=n.value}function u(d){r.push(d);const p=()=>{const v=r.indexOf(d);v>-1&&r.splice(v,1)};return o.push(p),p}function c(){const{history:d}=window;d.state&&d.replaceState(ce({},d.state,{scroll:js()}),"")}function f(){for(const d of o)d();o=[],window.removeEventListener("popstate",l),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",l),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function ei(e,t,n,s=!1,r=!1){return{back:e,current:t,forward:n,replaced:s,position:window.history.length,scroll:r?js():null}}function Wf(e){const{history:t,location:n}=window,s={value:na(e,n)},r={value:t.state};r.value||o(s.value,{back:null,current:s.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function o(a,u,c){const f=e.indexOf("#"),d=f>-1?(n.host&&document.querySelector("base")?e:e.slice(f))+a:zf()+e+a;try{t[c?"replaceState":"pushState"](u,"",d),r.value=u}catch(p){console.error(p),n[c?"replace":"assign"](d)}}function i(a,u){const c=ce({},t.state,ei(r.value.back,a,r.value.forward,!0),u,{position:r.value.position});o(a,c,!0),s.value=a}function l(a,u){const c=ce({},r.value,t.state,{forward:a,scroll:js()});o(c.current,c,!0);const f=ce({},ei(s.value,a,null),{position:c.position+1},u);o(a,f,!1),s.value=a}return{location:s,state:r,push:l,replace:i}}function Gf(e){e=jf(e);const t=Wf(e),n=Kf(e,t.state,t.location,t.replace);function s(o,i=!0){i||n.pauseListeners(),history.go(o)}const r=ce({location:"",base:e,go:s,createHref:Hf.bind(null,e)},t,n);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function Jf(e){return typeof e=="string"||e&&typeof e=="object"}function sa(e){return typeof e=="string"||typeof e=="symbol"}const ra=Symbol("");var ti;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(ti||(ti={}));function yn(e,t){return ce(new Error,{type:e,[ra]:!0},t)}function _t(e,t){return e instanceof Error&&ra in e&&(t==null||!!(e.type&t))}const ni="[^/]+?",Yf={sensitive:!1,strict:!1,start:!0,end:!0},Qf=/[.+*?^${}()[\]/\\]/g;function Xf(e,t){const n=ce({},Yf,t),s=[];let r=n.start?"^":"";const o=[];for(const u of e){const c=u.length?[]:[90];n.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const d=u[f];let p=40+(n.sensitive?.25:0);if(d.type===0)f||(r+="/"),r+=d.value.replace(Qf,"\\$&"),p+=40;else if(d.type===1){const{value:v,repeatable:y,optional:S,regexp:R}=d;o.push({name:v,repeatable:y,optional:S});const C=R||ni;if(C!==ni){p+=10;try{new RegExp(`(${C})`)}catch(O){throw new Error(`Invalid custom RegExp for param "${v}" (${C}): `+O.message)}}let D=y?`((?:${C})(?:/(?:${C}))*)`:`(${C})`;f||(D=S&&u.length<2?`(?:/${D})`:"/"+D),S&&(D+="?"),r+=D,p+=20,S&&(p+=-8),y&&(p+=-20),C===".*"&&(p+=-50)}c.push(p)}s.push(c)}if(n.strict&&n.end){const u=s.length-1;s[u][s[u].length-1]+=.7000000000000001}n.strict||(r+="/?"),n.end?r+="$":n.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const i=new RegExp(r,n.sensitive?"":"i");function l(u){const c=u.match(i),f={};if(!c)return null;for(let d=1;d<c.length;d++){const p=c[d]||"",v=o[d-1];f[v.name]=p&&v.repeatable?p.split("/"):p}return f}function a(u){let c="",f=!1;for(const d of e){(!f||!c.endsWith("/"))&&(c+="/"),f=!1;for(const p of d)if(p.type===0)c+=p.value;else if(p.type===1){const{value:v,repeatable:y,optional:S}=p,R=v in u?u[v]:"";if(it(R)&&!y)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const C=it(R)?R.join("/"):R;if(!C)if(S)d.length<2&&(c.endsWith("/")?c=c.slice(0,-1):f=!0);else throw new Error(`Missing required param "${v}"`);c+=C}}return c||"/"}return{re:i,score:s,keys:o,parse:l,stringify:a}}function Zf(e,t){let n=0;for(;n<e.length&&n<t.length;){const s=t[n]-e[n];if(s)return s;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function oa(e,t){let n=0;const s=e.score,r=t.score;for(;n<s.length&&n<r.length;){const o=Zf(s[n],r[n]);if(o)return o;n++}if(Math.abs(r.length-s.length)===1){if(si(s))return 1;if(si(r))return-1}return r.length-s.length}function si(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const ed={type:0,value:""},td=/[a-zA-Z0-9_]/;function nd(e){if(!e)return[[]];if(e==="/")return[[ed]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(p){throw new Error(`ERR (${n})/"${u}": ${p}`)}let n=0,s=n;const r=[];let o;function i(){o&&r.push(o),o=[]}let l=0,a,u="",c="";function f(){u&&(n===0?o.push({type:0,value:u}):n===1||n===2||n===3?(o.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),o.push({type:1,value:u,regexp:c,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function d(){u+=a}for(;l<e.length;){if(a=e[l++],a==="\\"&&n!==2){s=n,n=4;continue}switch(n){case 0:a==="/"?(u&&f(),i()):a===":"?(f(),n=1):d();break;case 4:d(),n=s;break;case 1:a==="("?n=2:td.test(a)?d():(f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--);break;case 2:a===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+a:n=3:c+=a;break;case 3:f(),n=0,a!=="*"&&a!=="?"&&a!=="+"&&l--,c="";break;default:t("Unknown state");break}}return n===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),i(),r}function sd(e,t,n){const s=Xf(nd(e.path),n),r=ce(s,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function rd(e,t){const n=[],s=new Map;t=li({strict:!1,end:!0,sensitive:!1},t);function r(f){return s.get(f)}function o(f,d,p){const v=!p,y=oi(f);y.aliasOf=p&&p.record;const S=li(t,f),R=[y];if("alias"in f){const O=typeof f.alias=="string"?[f.alias]:f.alias;for(const I of O)R.push(oi(ce({},y,{components:p?p.record.components:y.components,path:I,aliasOf:p?p.record:y})))}let C,D;for(const O of R){const{path:I}=O;if(d&&I[0]!=="/"){const M=d.record.path,P=M[M.length-1]==="/"?"":"/";O.path=d.record.path+(I&&P+I)}if(C=sd(O,d,S),p?p.alias.push(C):(D=D||C,D!==C&&D.alias.push(C),v&&f.name&&!ii(C)&&i(f.name)),ia(C)&&a(C),y.children){const M=y.children;for(let P=0;P<M.length;P++)o(M[P],C,p&&p.children[P])}p=p||C}return D?()=>{i(D)}:Nn}function i(f){if(sa(f)){const d=s.get(f);d&&(s.delete(f),n.splice(n.indexOf(d),1),d.children.forEach(i),d.alias.forEach(i))}else{const d=n.indexOf(f);d>-1&&(n.splice(d,1),f.record.name&&s.delete(f.record.name),f.children.forEach(i),f.alias.forEach(i))}}function l(){return n}function a(f){const d=ld(f,n);n.splice(d,0,f),f.record.name&&!ii(f)&&s.set(f.record.name,f)}function u(f,d){let p,v={},y,S;if("name"in f&&f.name){if(p=s.get(f.name),!p)throw yn(1,{location:f});S=p.record.name,v=ce(ri(d.params,p.keys.filter(D=>!D.optional).concat(p.parent?p.parent.keys.filter(D=>D.optional):[]).map(D=>D.name)),f.params&&ri(f.params,p.keys.map(D=>D.name))),y=p.stringify(v)}else if(f.path!=null)y=f.path,p=n.find(D=>D.re.test(y)),p&&(v=p.parse(y),S=p.record.name);else{if(p=d.name?s.get(d.name):n.find(D=>D.re.test(d.path)),!p)throw yn(1,{location:f,currentLocation:d});S=p.record.name,v=ce({},d.params,f.params),y=p.stringify(v)}const R=[];let C=p;for(;C;)R.unshift(C.record),C=C.parent;return{name:S,path:y,params:v,matched:R,meta:id(R)}}e.forEach(f=>o(f));function c(){n.length=0,s.clear()}return{addRoute:o,resolve:u,removeRoute:i,clearRoutes:c,getRoutes:l,getRecordMatcher:r}}function ri(e,t){const n={};for(const s of t)s in e&&(n[s]=e[s]);return n}function oi(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:od(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function od(e){const t={},n=e.props||!1;if("component"in e)t.default=n;else for(const s in e.components)t[s]=typeof n=="object"?n[s]:n;return t}function ii(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function id(e){return e.reduce((t,n)=>ce(t,n.meta),{})}function li(e,t){const n={};for(const s in e)n[s]=s in t?t[s]:e[s];return n}function ld(e,t){let n=0,s=t.length;for(;n!==s;){const o=n+s>>1;oa(e,t[o])<0?s=o:n=o+1}const r=ad(e);return r&&(s=t.lastIndexOf(r,s-1)),s}function ad(e){let t=e;for(;t=t.parent;)if(ia(t)&&oa(e,t)===0)return t}function ia({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function cd(e){const t={};if(e===""||e==="?")return t;const s=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const o=s[r].replace(Ql," "),i=o.indexOf("="),l=Gn(i<0?o:o.slice(0,i)),a=i<0?null:Gn(o.slice(i+1));if(l in t){let u=t[l];it(u)||(u=t[l]=[u]),u.push(a)}else t[l]=a}return t}function ai(e){let t="";for(let n in e){const s=e[n];if(n=Of(n),s==null){s!==void 0&&(t+=(t.length?"&":"")+n);continue}(it(s)?s.map(o=>o&&Dr(o)):[s&&Dr(s)]).forEach(o=>{o!==void 0&&(t+=(t.length?"&":"")+n,o!=null&&(t+="="+o))})}return t}function ud(e){const t={};for(const n in e){const s=e[n];s!==void 0&&(t[n]=it(s)?s.map(r=>r==null?null:""+r):s==null?s:""+s)}return t}const fd=Symbol(""),ci=Symbol(""),Fs=Symbol(""),la=Symbol(""),$r=Symbol("");function Rn(){let e=[];function t(s){return e.push(s),()=>{const r=e.indexOf(s);r>-1&&e.splice(r,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function It(e,t,n,s,r,o=i=>i()){const i=s&&(s.enterCallbacks[r]=s.enterCallbacks[r]||[]);return()=>new Promise((l,a)=>{const u=d=>{d===!1?a(yn(4,{from:n,to:t})):d instanceof Error?a(d):Jf(d)?a(yn(2,{from:t,to:d})):(i&&s.enterCallbacks[r]===i&&typeof d=="function"&&i.push(d),l())},c=o(()=>e.call(s&&s.instances[r],t,n,u));let f=Promise.resolve(c);e.length<3&&(f=f.then(u)),f.catch(d=>a(d))})}function cr(e,t,n,s,r=o=>o()){const o=[];for(const i of e)for(const l in i.components){let a=i.components[l];if(!(t!=="beforeRouteEnter"&&!i.instances[l]))if(Jl(a)){const c=(a.__vccOpts||a)[t];c&&o.push(It(c,n,s,i,l,r))}else{let u=a();o.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${l}" at "${i.path}"`);const f=yf(c)?c.default:c;i.mods[l]=c,i.components[l]=f;const p=(f.__vccOpts||f)[t];return p&&It(p,n,s,i,l,r)()}))}}return o}function ui(e){const t=tt(Fs),n=tt(la),s=Ye(()=>{const a=B(e.to);return t.resolve(a)}),r=Ye(()=>{const{matched:a}=s.value,{length:u}=a,c=a[u-1],f=n.matched;if(!c||!f.length)return-1;const d=f.findIndex(vn.bind(null,c));if(d>-1)return d;const p=fi(a[u-2]);return u>1&&fi(c)===p&&f[f.length-1].path!==p?f.findIndex(vn.bind(null,a[u-2])):d}),o=Ye(()=>r.value>-1&&gd(n.params,s.value.params)),i=Ye(()=>r.value>-1&&r.value===n.matched.length-1&&ta(n.params,s.value.params));function l(a={}){if(md(a)){const u=t[B(e.replace)?"replace":"push"](B(e.to)).catch(Nn);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:s,href:Ye(()=>s.value.href),isActive:o,isExactActive:i,navigate:l}}function dd(e){return e.length===1?e[0]:e}const pd=At({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ui,setup(e,{slots:t}){const n=Ct(ui(e)),{options:s}=tt(Fs),r=Ye(()=>({[di(e.activeClass,s.linkActiveClass,"router-link-active")]:n.isActive,[di(e.exactActiveClass,s.linkExactActiveClass,"router-link-exact-active")]:n.isExactActive}));return()=>{const o=t.default&&dd(t.default(n));return e.custom?o:ro("a",{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:r.value},o)}}}),hd=pd;function md(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function gd(e,t){for(const n in t){const s=t[n],r=e[n];if(typeof s=="string"){if(s!==r)return!1}else if(!it(r)||r.length!==s.length||s.some((o,i)=>o!==r[i]))return!1}return!0}function fi(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const di=(e,t,n)=>e??t??n,vd=At({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){const s=tt($r),r=Ye(()=>e.route||s.value),o=tt(ci,0),i=Ye(()=>{let u=B(o);const{matched:c}=r.value;let f;for(;(f=c[u])&&!f.components;)u++;return u}),l=Ye(()=>r.value.matched[i.value]);en(ci,Ye(()=>i.value+1)),en(fd,l),en($r,r);const a=z();return tn(()=>[a.value,l.value,e.name],([u,c,f],[d,p,v])=>{c&&(c.instances[f]=u,p&&p!==c&&u&&u===d&&(c.leaveGuards.size||(c.leaveGuards=p.leaveGuards),c.updateGuards.size||(c.updateGuards=p.updateGuards))),u&&c&&(!p||!vn(c,p)||!d)&&(c.enterCallbacks[f]||[]).forEach(y=>y(u))},{flush:"post"}),()=>{const u=r.value,c=e.name,f=l.value,d=f&&f.components[c];if(!d)return pi(n.default,{Component:d,route:u});const p=f.props[c],v=p?p===!0?u.params:typeof p=="function"?p(u):p:null,S=ro(d,ce({},v,t,{onVnodeUnmounted:R=>{R.component.isUnmounted&&(f.instances[c]=null)},ref:a}));return pi(n.default,{Component:S,route:u})||S}}});function pi(e,t){if(!e)return null;const n=e(t);return n.length===1?n[0]:n}const yd=vd;function bd(e){const t=rd(e.routes,e),n=e.parseQuery||cd,s=e.stringifyQuery||ai,r=e.history,o=Rn(),i=Rn(),l=Rn(),a=hc(Dt);let u=Dt;un&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=lr.bind(null,w=>""+w),f=lr.bind(null,Df),d=lr.bind(null,Gn);function p(w,j){let N,q;return sa(w)?(N=t.getRecordMatcher(w),q=j):q=w,t.addRoute(q,N)}function v(w){const j=t.getRecordMatcher(w);j&&t.removeRoute(j)}function y(){return t.getRoutes().map(w=>w.record)}function S(w){return!!t.getRecordMatcher(w)}function R(w,j){if(j=ce({},j||a.value),typeof w=="string"){const b=ar(n,w,j.path),T=t.resolve({path:b.path},j),x=r.createHref(b.fullPath);return ce(b,T,{params:d(T.params),hash:Gn(b.hash),redirectedFrom:void 0,href:x})}let N;if(w.path!=null)N=ce({},w,{path:ar(n,w.path,j.path).path});else{const b=ce({},w.params);for(const T in b)b[T]==null&&delete b[T];N=ce({},w,{params:f(b)}),j.params=f(j.params)}const q=t.resolve(N,j),he=w.hash||"";q.params=c(d(q.params));const h=Lf(s,ce({},w,{hash:Af(he),path:q.path})),m=r.createHref(h);return ce({fullPath:h,hash:he,query:s===ai?ud(w.query):w.query||{}},q,{redirectedFrom:void 0,href:m})}function C(w){return typeof w=="string"?ar(n,w,a.value.path):ce({},w)}function D(w,j){if(u!==w)return yn(8,{from:j,to:w})}function O(w){return P(w)}function I(w){return O(ce(C(w),{replace:!0}))}function M(w){const j=w.matched[w.matched.length-1];if(j&&j.redirect){const{redirect:N}=j;let q=typeof N=="function"?N(w):N;return typeof q=="string"&&(q=q.includes("?")||q.includes("#")?q=C(q):{path:q},q.params={}),ce({query:w.query,hash:w.hash,params:q.path!=null?{}:w.params},q)}}function P(w,j){const N=u=R(w),q=a.value,he=w.state,h=w.force,m=w.replace===!0,b=M(N);if(b)return P(ce(C(b),{state:typeof b=="object"?ce({},he,b.state):he,force:h,replace:m}),j||N);const T=N;T.redirectedFrom=j;let x;return!h&&If(s,q,N)&&(x=yn(16,{to:T,from:q}),ke(q,q,!0,!1)),(x?Promise.resolve(x):se(T,q)).catch(E=>_t(E)?_t(E,2)?E:at(E):ie(E,T,q)).then(E=>{if(E){if(_t(E,2))return P(ce({replace:m},C(E.to),{state:typeof E.to=="object"?ce({},he,E.to.state):he,force:h}),j||T)}else E=H(T,q,!0,m,he);return pe(T,q,E),E})}function U(w,j){const N=D(w,j);return N?Promise.reject(N):Promise.resolve()}function V(w){const j=te.values().next().value;return j&&typeof j.runWithContext=="function"?j.runWithContext(w):w()}function se(w,j){let N;const[q,he,h]=_d(w,j);N=cr(q.reverse(),"beforeRouteLeave",w,j);for(const b of q)b.leaveGuards.forEach(T=>{N.push(It(T,w,j))});const m=U.bind(null,w,j);return N.push(m),xe(N).then(()=>{N=[];for(const b of o.list())N.push(It(b,w,j));return N.push(m),xe(N)}).then(()=>{N=cr(he,"beforeRouteUpdate",w,j);for(const b of he)b.updateGuards.forEach(T=>{N.push(It(T,w,j))});return N.push(m),xe(N)}).then(()=>{N=[];for(const b of h)if(b.beforeEnter)if(it(b.beforeEnter))for(const T of b.beforeEnter)N.push(It(T,w,j));else N.push(It(b.beforeEnter,w,j));return N.push(m),xe(N)}).then(()=>(w.matched.forEach(b=>b.enterCallbacks={}),N=cr(h,"beforeRouteEnter",w,j,V),N.push(m),xe(N))).then(()=>{N=[];for(const b of i.list())N.push(It(b,w,j));return N.push(m),xe(N)}).catch(b=>_t(b,8)?b:Promise.reject(b))}function pe(w,j,N){l.list().forEach(q=>V(()=>q(w,j,N)))}function H(w,j,N,q,he){const h=D(w,j);if(h)return h;const m=j===Dt,b=un?history.state:{};N&&(q||m?r.replace(w.fullPath,ce({scroll:m&&b&&b.scroll},he)):r.push(w.fullPath,he)),a.value=w,ke(w,j,N,m),at()}let oe;function Ee(){oe||(oe=r.listen((w,j,N)=>{if(!be.listening)return;const q=R(w),he=M(q);if(he){P(ce(he,{replace:!0,force:!0}),q).catch(Nn);return}u=q;const h=a.value;un&&Vf(Zo(h.fullPath,N.delta),js()),se(q,h).catch(m=>_t(m,12)?m:_t(m,2)?(P(ce(C(m.to),{force:!0}),q).then(b=>{_t(b,20)&&!N.delta&&N.type===Jn.pop&&r.go(-1,!1)}).catch(Nn),Promise.reject()):(N.delta&&r.go(-N.delta,!1),ie(m,q,h))).then(m=>{m=m||H(q,h,!1),m&&(N.delta&&!_t(m,8)?r.go(-N.delta,!1):N.type===Jn.pop&&_t(m,20)&&r.go(-1,!1)),pe(q,h,m)}).catch(Nn)}))}let Ie=Rn(),fe=Rn(),ae;function ie(w,j,N){at(w);const q=fe.list();return q.length?q.forEach(he=>he(w,j,N)):console.error(w),Promise.reject(w)}function st(){return ae&&a.value!==Dt?Promise.resolve():new Promise((w,j)=>{Ie.add([w,j])})}function at(w){return ae||(ae=!w,Ee(),Ie.list().forEach(([j,N])=>w?N(w):j()),Ie.reset()),w}function ke(w,j,N,q){const{scrollBehavior:he}=e;if(!un||!he)return Promise.resolve();const h=!N&&qf(Zo(w.fullPath,0))||(q||!N)&&history.state&&history.state.scroll||null;return ol().then(()=>he(w,j,h)).then(m=>m&&Uf(m)).catch(m=>ie(m,w,j))}const _e=w=>r.go(w);let Q;const te=new Set,be={currentRoute:a,listening:!0,addRoute:p,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:S,getRoutes:y,resolve:R,options:e,push:O,replace:I,go:_e,back:()=>_e(-1),forward:()=>_e(1),beforeEach:o.add,beforeResolve:i.add,afterEach:l.add,onError:fe.add,isReady:st,install(w){const j=this;w.component("RouterLink",hd),w.component("RouterView",yd),w.config.globalProperties.$router=j,Object.defineProperty(w.config.globalProperties,"$route",{enumerable:!0,get:()=>B(a)}),un&&!Q&&a.value===Dt&&(Q=!0,O(r.location).catch(he=>{}));const N={};for(const he in Dt)Object.defineProperty(N,he,{get:()=>a.value[he],enumerable:!0});w.provide(Fs,j),w.provide(la,Zi(N)),w.provide($r,a);const q=w.unmount;te.add(w),w.unmount=function(){te.delete(w),te.size<1&&(u=Dt,oe&&oe(),oe=null,a.value=Dt,Q=!1,ae=!1),q()}}};function xe(w){return w.reduce((j,N)=>j.then(()=>V(N)),Promise.resolve())}return be}function _d(e,t){const n=[],s=[],r=[],o=Math.max(t.matched.length,e.matched.length);for(let i=0;i<o;i++){const l=t.matched[i];l&&(e.matched.find(u=>vn(u,l))?s.push(l):n.push(l));const a=e.matched[i];a&&(t.matched.find(u=>vn(u,a))||r.push(a))}return[n,s,r]}function aa(){return tt(Fs)}const Sd={class:"container"},wd={class:"content"},Td=At({__name:"App",setup(e){const t=z(null),n=z(null);en("mainRef",t),en("headerRef",n);const{notification:s,hideNotification:r}=gf(),o=aa(),i=()=>{o.push("/settings")},l=()=>{o.push("/")};return en("notification",{info:vf,success:Pr,warning:Gl,error:gn}),(a,u)=>{const c=Fc("RouterView");return Z(),ne("div",Sd,[g("div",{class:"main",ref_key:"mainRef",ref:t},[g("header",{ref_key:"headerRef",ref:n},[u[0]||(u[0]=g("div",{class:"logo"},[g("img",{src:"data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzFhMzY1ZCIgZD0iTTEyIDJDNi40OCAyIDIgNi40OCAyIDEyczQuNDggMTAgMTAgMTAgMTAtNC40OCAxMC0xMFMxNy41MiAyIDEyIDJ6bS0xLjQxIDEwLjA5YzEuMDcgMCAxLjkzLS44NiAxLjkzLTEuOTNzLS44Ni0xLjkzLTEuOTMtMS45My0xLjkzLjg2LTEuOTMgMS45My44NiAxLjkzIDEuOTMgMS45M3ptNi40MSA1LjI5Yy0uMTQtLjQtLjc2LS43LTEuMTktLjc5LS40My0uMDktMi45NS0uMTEtMy40Ny0uMTEtLjUyIDAtMy4wNC4wMi0zLjQ3LjExLS40My4wOS0xLjA1LjM5LTEuMTkuNzktLjE0LjQtLjI2IDEuMzUtLjI2IDEuNzQgMCAuMzkuMTIgMS4zNC4yNiAxLjc0LjE0LjQuNzYuNyAxLjE5Ljc5LjQzLjA5IDIuOTUuMTEgMy40Ny4xMS41MiAwIDMuMDQtLjAyIDMuNDctLjExLjQzLS4wOSAxLjA1LS4zOSAxLjE5LS43OS4xNC0uNC4yNi0xLjM1LjI2LTEuNzQgMC0uMzktLjEyLTEuMzQtLjI2LTEuNzR6bS0xLjg4LTcuMzZjMS4wNyAwIDEuOTMtLjg2IDEuOTMtMS45M3MtLjg2LTEuOTMtMS45My0xLjkzLTEuOTMuODYtMS45MyAxLjkzLjg2IDEuOTMgMS45MyAxLjkzeiIvPjwvc3ZnPg==",alt:"穷游旅行"}),g("span",{style:{"margin-left":"8px",color:"#1a365d","font-weight":"bold"}},"穷游旅行")],-1)),g("div",{class:"nav-links"},[g("a",{href:"#",onClick:l},"首页"),g("a",{href:"#",class:"login-btn",onClick:i},"设置")])],512),g("div",wd,[Re(c)])],512),Re(Or,{show:B(s).show,type:B(s).type,title:B(s).title,message:B(s).message,duration:B(s).duration,position:B(s).position,onClose:B(r)},null,8,["show","type","title","message","duration","position","onClose"]),Re(Or,{show:B(Me).show,type:B(Me).type,title:B(Me).title,message:B(Me).message,duration:B(Me).duration,position:B(Me).position,onClose:B(Wl)},null,8,["show","type","title","message","duration","position","onClose"])])}}});function Lr(e={}){const{multiple:t=!1,initialSelection:n,onSelect:s,onClear:r}=e,o=z(n?Array.isArray(n)?[...n]:[n]:[]);return{selectedItems:o,selectItem:(c,f=!0)=>{if(t)if(f)o.value=[c];else{const d=o.value.findIndex(p=>typeof c=="object"&&c!==null?JSON.stringify(p)===JSON.stringify(c):p===c);d!==-1?o.value=[...o.value.slice(0,d),...o.value.slice(d+1)]:o.value=[...o.value,c]}else o.value=[c];return s&&s(t?o.value:o.value[0]),t?o.value:o.value[0]},isSelected:c=>o.value.findIndex(f=>typeof c=="object"&&c!==null?JSON.stringify(f)===JSON.stringify(c):f===c)!==-1,clearSelection:()=>{o.value=[],r&&r()},getSelection:()=>t?o.value:o.value[0]||null}}function Ir(e={}){const{hideScrollbar:t=!0,autoScrollToTop:n=!0,onScroll:s}=e,r=z(null),o=z(0),i=z(!1),l=p=>{r.value&&(p.stopPropagation(),o.value=r.value.scrollTop,i.value=!0,clearTimeout(a),a=setTimeout(()=>{i.value=!1},150),s&&s(p),setTimeout(()=>{r.value&&(o.value=r.value.scrollTop)},0))};let a;const u=(p=!0)=>{r.value&&(p?r.value.scrollTo({top:0,behavior:"smooth"}):r.value.scrollTop=0)},c=(p=!0)=>{r.value&&(p?r.value.scrollTo({top:r.value.scrollHeight,behavior:"smooth"}):r.value.scrollTop=r.value.scrollHeight)},f=(p,v=!0,y=0)=>{if(!r.value||!p)return;const S=p.offsetTop;v?r.value.scrollTo({top:S-y,behavior:"smooth"}):r.value.scrollTop=S-y},d=p=>{r.value&&(p?r.value.style.overflowY="auto":r.value.style.overflowY="hidden")};return on(()=>{r.value&&(r.value.addEventListener("scroll",l),t&&r.value.classList.add("scrollable-hidden"),n&&u(!1))}),Xn(()=>{r.value&&r.value.removeEventListener("scroll",l),clearTimeout(a)}),{containerRef:r,scrollPosition:o,isScrolling:i,scrollToTop:u,scrollToBottom:c,scrollToElement:f,setScrollingEnabled:d}}function ca(e,t){return function(){return e.apply(t,arguments)}}const{toString:Ed}=Object.prototype,{getPrototypeOf:io}=Object,{iterator:Hs,toStringTag:ua}=Symbol,Bs=(e=>t=>{const n=Ed.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),lt=e=>(e=e.toLowerCase(),t=>Bs(t)===e),Us=e=>t=>typeof t===e,{isArray:bn}=Array,Yn=Us("undefined");function xd(e){return e!==null&&!Yn(e)&&e.constructor!==null&&!Yn(e.constructor)&&qe(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const fa=lt("ArrayBuffer");function Rd(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&fa(e.buffer),t}const Cd=Us("string"),qe=Us("function"),da=Us("number"),Vs=e=>e!==null&&typeof e=="object",Ad=e=>e===!0||e===!1,us=e=>{if(Bs(e)!=="object")return!1;const t=io(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(ua in e)&&!(Hs in e)},Od=lt("Date"),Pd=lt("File"),Dd=lt("Blob"),Md=lt("FileList"),$d=e=>Vs(e)&&qe(e.pipe),Ld=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||qe(e.append)&&((t=Bs(e))==="formdata"||t==="object"&&qe(e.toString)&&e.toString()==="[object FormData]"))},Id=lt("URLSearchParams"),[kd,Nd,jd,Fd]=["ReadableStream","Request","Response","Headers"].map(lt),Hd=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function es(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),bn(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let l;for(s=0;s<i;s++)l=o[s],t.call(null,e[l],l,e)}}function pa(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const Xt=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,ha=e=>!Yn(e)&&e!==Xt;function kr(){const{caseless:e}=ha(this)&&this||{},t={},n=(s,r)=>{const o=e&&pa(t,r)||r;us(t[o])&&us(s)?t[o]=kr(t[o],s):us(s)?t[o]=kr({},s):bn(s)?t[o]=s.slice():t[o]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&es(arguments[s],n);return t}const Bd=(e,t,n,{allOwnKeys:s}={})=>(es(t,(r,o)=>{n&&qe(r)?e[o]=ca(r,n):e[o]=r},{allOwnKeys:s}),e),Ud=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Vd=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},qd=(e,t,n,s)=>{let r,o,i;const l={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),o=r.length;o-- >0;)i=r[o],(!s||s(i,e,t))&&!l[i]&&(t[i]=e[i],l[i]=!0);e=n!==!1&&io(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},zd=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},Kd=e=>{if(!e)return null;if(bn(e))return e;let t=e.length;if(!da(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Wd=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&io(Uint8Array)),Gd=(e,t)=>{const s=(e&&e[Hs]).call(e);let r;for(;(r=s.next())&&!r.done;){const o=r.value;t.call(e,o[0],o[1])}},Jd=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},Yd=lt("HTMLFormElement"),Qd=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),hi=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Xd=lt("RegExp"),ma=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};es(n,(r,o)=>{let i;(i=t(r,o,e))!==!1&&(s[o]=i||r)}),Object.defineProperties(e,s)},Zd=e=>{ma(e,(t,n)=>{if(qe(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(qe(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},ep=(e,t)=>{const n={},s=r=>{r.forEach(o=>{n[o]=!0})};return bn(e)?s(e):s(String(e).split(t)),n},tp=()=>{},np=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function sp(e){return!!(e&&qe(e.append)&&e[ua]==="FormData"&&e[Hs])}const rp=e=>{const t=new Array(10),n=(s,r)=>{if(Vs(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const o=bn(s)?[]:{};return es(s,(i,l)=>{const a=n(i,r+1);!Yn(a)&&(o[l]=a)}),t[r]=void 0,o}}return s};return n(e,0)},op=lt("AsyncFunction"),ip=e=>e&&(Vs(e)||qe(e))&&qe(e.then)&&qe(e.catch),ga=((e,t)=>e?setImmediate:t?((n,s)=>(Xt.addEventListener("message",({source:r,data:o})=>{r===Xt&&o===n&&s.length&&s.shift()()},!1),r=>{s.push(r),Xt.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",qe(Xt.postMessage)),lp=typeof queueMicrotask<"u"?queueMicrotask.bind(Xt):typeof process<"u"&&process.nextTick||ga,ap=e=>e!=null&&qe(e[Hs]),_={isArray:bn,isArrayBuffer:fa,isBuffer:xd,isFormData:Ld,isArrayBufferView:Rd,isString:Cd,isNumber:da,isBoolean:Ad,isObject:Vs,isPlainObject:us,isReadableStream:kd,isRequest:Nd,isResponse:jd,isHeaders:Fd,isUndefined:Yn,isDate:Od,isFile:Pd,isBlob:Dd,isRegExp:Xd,isFunction:qe,isStream:$d,isURLSearchParams:Id,isTypedArray:Wd,isFileList:Md,forEach:es,merge:kr,extend:Bd,trim:Hd,stripBOM:Ud,inherits:Vd,toFlatObject:qd,kindOf:Bs,kindOfTest:lt,endsWith:zd,toArray:Kd,forEachEntry:Gd,matchAll:Jd,isHTMLForm:Yd,hasOwnProperty:hi,hasOwnProp:hi,reduceDescriptors:ma,freezeMethods:Zd,toObjectSet:ep,toCamelCase:Qd,noop:tp,toFiniteNumber:np,findKey:pa,global:Xt,isContextDefined:ha,isSpecCompliantForm:sp,toJSONObject:rp,isAsyncFn:op,isThenable:ip,setImmediate:ga,asap:lp,isIterable:ap};function ee(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}_.inherits(ee,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:_.toJSONObject(this.config),code:this.code,status:this.status}}});const va=ee.prototype,ya={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ya[e]={value:e}});Object.defineProperties(ee,ya);Object.defineProperty(va,"isAxiosError",{value:!0});ee.from=(e,t,n,s,r,o)=>{const i=Object.create(va);return _.toFlatObject(e,i,function(a){return a!==Error.prototype},l=>l!=="isAxiosError"),ee.call(i,e.message,t,n,s,r),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const cp=null;function Nr(e){return _.isPlainObject(e)||_.isArray(e)}function ba(e){return _.endsWith(e,"[]")?e.slice(0,-2):e}function mi(e,t,n){return e?e.concat(t).map(function(r,o){return r=ba(r),!n&&o?"["+r+"]":r}).join(n?".":""):t}function up(e){return _.isArray(e)&&!e.some(Nr)}const fp=_.toFlatObject(_,{},null,function(t){return/^is[A-Z]/.test(t)});function qs(e,t,n){if(!_.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=_.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,S){return!_.isUndefined(S[y])});const s=n.metaTokens,r=n.visitor||c,o=n.dots,i=n.indexes,a=(n.Blob||typeof Blob<"u"&&Blob)&&_.isSpecCompliantForm(t);if(!_.isFunction(r))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(_.isDate(v))return v.toISOString();if(!a&&_.isBlob(v))throw new ee("Blob is not supported. Use a Buffer instead.");return _.isArrayBuffer(v)||_.isTypedArray(v)?a&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,y,S){let R=v;if(v&&!S&&typeof v=="object"){if(_.endsWith(y,"{}"))y=s?y:y.slice(0,-2),v=JSON.stringify(v);else if(_.isArray(v)&&up(v)||(_.isFileList(v)||_.endsWith(y,"[]"))&&(R=_.toArray(v)))return y=ba(y),R.forEach(function(D,O){!(_.isUndefined(D)||D===null)&&t.append(i===!0?mi([y],O,o):i===null?y:y+"[]",u(D))}),!1}return Nr(v)?!0:(t.append(mi(S,y,o),u(v)),!1)}const f=[],d=Object.assign(fp,{defaultVisitor:c,convertValue:u,isVisitable:Nr});function p(v,y){if(!_.isUndefined(v)){if(f.indexOf(v)!==-1)throw Error("Circular reference detected in "+y.join("."));f.push(v),_.forEach(v,function(R,C){(!(_.isUndefined(R)||R===null)&&r.call(t,R,_.isString(C)?C.trim():C,y,d))===!0&&p(R,y?y.concat(C):[C])}),f.pop()}}if(!_.isObject(e))throw new TypeError("data must be an object");return p(e),t}function gi(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function lo(e,t){this._pairs=[],e&&qs(e,this,t)}const _a=lo.prototype;_a.append=function(t,n){this._pairs.push([t,n])};_a.toString=function(t){const n=t?function(s){return t.call(this,s,gi)}:gi;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function dp(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Sa(e,t,n){if(!t)return e;const s=n&&n.encode||dp;_.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let o;if(r?o=r(t,n):o=_.isURLSearchParams(t)?t.toString():new lo(t,n).toString(s),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class vi{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){_.forEach(this.handlers,function(s){s!==null&&t(s)})}}const wa={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},pp=typeof URLSearchParams<"u"?URLSearchParams:lo,hp=typeof FormData<"u"?FormData:null,mp=typeof Blob<"u"?Blob:null,gp={isBrowser:!0,classes:{URLSearchParams:pp,FormData:hp,Blob:mp},protocols:["http","https","file","blob","url","data"]},ao=typeof window<"u"&&typeof document<"u",jr=typeof navigator=="object"&&navigator||void 0,vp=ao&&(!jr||["ReactNative","NativeScript","NS"].indexOf(jr.product)<0),yp=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",bp=ao&&window.location.href||"http://localhost",_p=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ao,hasStandardBrowserEnv:vp,hasStandardBrowserWebWorkerEnv:yp,navigator:jr,origin:bp},Symbol.toStringTag,{value:"Module"})),$e={..._p,...gp};function Sp(e,t){return qs(e,new $e.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,o){return $e.isNode&&_.isBuffer(n)?(this.append(s,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function wp(e){return _.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Tp(e){const t={},n=Object.keys(e);let s;const r=n.length;let o;for(s=0;s<r;s++)o=n[s],t[o]=e[o];return t}function Ta(e){function t(n,s,r,o){let i=n[o++];if(i==="__proto__")return!0;const l=Number.isFinite(+i),a=o>=n.length;return i=!i&&_.isArray(r)?r.length:i,a?(_.hasOwnProp(r,i)?r[i]=[r[i],s]:r[i]=s,!l):((!r[i]||!_.isObject(r[i]))&&(r[i]=[]),t(n,s,r[i],o)&&_.isArray(r[i])&&(r[i]=Tp(r[i])),!l)}if(_.isFormData(e)&&_.isFunction(e.entries)){const n={};return _.forEachEntry(e,(s,r)=>{t(wp(s),r,n,0)}),n}return null}function Ep(e,t,n){if(_.isString(e))try{return(t||JSON.parse)(e),_.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const ts={transitional:wa,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,o=_.isObject(t);if(o&&_.isHTMLForm(t)&&(t=new FormData(t)),_.isFormData(t))return r?JSON.stringify(Ta(t)):t;if(_.isArrayBuffer(t)||_.isBuffer(t)||_.isStream(t)||_.isFile(t)||_.isBlob(t)||_.isReadableStream(t))return t;if(_.isArrayBufferView(t))return t.buffer;if(_.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let l;if(o){if(s.indexOf("application/x-www-form-urlencoded")>-1)return Sp(t,this.formSerializer).toString();if((l=_.isFileList(t))||s.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return qs(l?{"files[]":t}:t,a&&new a,this.formSerializer)}}return o||r?(n.setContentType("application/json",!1),Ep(t)):t}],transformResponse:[function(t){const n=this.transitional||ts.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(_.isResponse(t)||_.isReadableStream(t))return t;if(t&&_.isString(t)&&(s&&!this.responseType||r)){const i=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(l){if(i)throw l.name==="SyntaxError"?ee.from(l,ee.ERR_BAD_RESPONSE,this,null,this.response):l}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:$e.classes.FormData,Blob:$e.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};_.forEach(["delete","get","head","post","put","patch"],e=>{ts.headers[e]={}});const xp=_.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Rp=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(i){r=i.indexOf(":"),n=i.substring(0,r).trim().toLowerCase(),s=i.substring(r+1).trim(),!(!n||t[n]&&xp[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},yi=Symbol("internals");function Cn(e){return e&&String(e).trim().toLowerCase()}function fs(e){return e===!1||e==null?e:_.isArray(e)?e.map(fs):String(e)}function Cp(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Ap=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function ur(e,t,n,s,r){if(_.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!_.isString(t)){if(_.isString(s))return t.indexOf(s)!==-1;if(_.isRegExp(s))return s.test(t)}}function Op(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Pp(e,t){const n=_.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,o,i){return this[s].call(this,t,r,o,i)},configurable:!0})})}let ze=class{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function o(l,a,u){const c=Cn(a);if(!c)throw new Error("header name must be a non-empty string");const f=_.findKey(r,c);(!f||r[f]===void 0||u===!0||u===void 0&&r[f]!==!1)&&(r[f||a]=fs(l))}const i=(l,a)=>_.forEach(l,(u,c)=>o(u,c,a));if(_.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(_.isString(t)&&(t=t.trim())&&!Ap(t))i(Rp(t),n);else if(_.isObject(t)&&_.isIterable(t)){let l={},a,u;for(const c of t){if(!_.isArray(c))throw TypeError("Object iterator must return a key-value pair");l[u=c[0]]=(a=l[u])?_.isArray(a)?[...a,c[1]]:[a,c[1]]:c[1]}i(l,n)}else t!=null&&o(n,t,s);return this}get(t,n){if(t=Cn(t),t){const s=_.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Cp(r);if(_.isFunction(n))return n.call(this,r,s);if(_.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=Cn(t),t){const s=_.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||ur(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function o(i){if(i=Cn(i),i){const l=_.findKey(s,i);l&&(!n||ur(s,s[l],l,n))&&(delete s[l],r=!0)}}return _.isArray(t)?t.forEach(o):o(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const o=n[s];(!t||ur(this,this[o],o,t,!0))&&(delete this[o],r=!0)}return r}normalize(t){const n=this,s={};return _.forEach(this,(r,o)=>{const i=_.findKey(s,o);if(i){n[i]=fs(r),delete n[o];return}const l=t?Op(o):String(o).trim();l!==o&&delete n[o],n[l]=fs(r),s[l]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return _.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&_.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[yi]=this[yi]={accessors:{}}).accessors,r=this.prototype;function o(i){const l=Cn(i);s[l]||(Pp(r,i),s[l]=!0)}return _.isArray(t)?t.forEach(o):o(t),this}};ze.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);_.reduceDescriptors(ze.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});_.freezeMethods(ze);function fr(e,t){const n=this||ts,s=t||n,r=ze.from(s.headers);let o=s.data;return _.forEach(e,function(l){o=l.call(n,o,r.normalize(),t?t.status:void 0)}),r.normalize(),o}function Ea(e){return!!(e&&e.__CANCEL__)}function _n(e,t,n){ee.call(this,e??"canceled",ee.ERR_CANCELED,t,n),this.name="CanceledError"}_.inherits(_n,ee,{__CANCEL__:!0});function xa(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new ee("Request failed with status code "+n.status,[ee.ERR_BAD_REQUEST,ee.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Dp(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Mp(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,o=0,i;return t=t!==void 0?t:1e3,function(a){const u=Date.now(),c=s[o];i||(i=u),n[r]=a,s[r]=u;let f=o,d=0;for(;f!==r;)d+=n[f++],f=f%e;if(r=(r+1)%e,r===o&&(o=(o+1)%e),u-i<t)return;const p=c&&u-c;return p?Math.round(d*1e3/p):void 0}}function $p(e,t){let n=0,s=1e3/t,r,o;const i=(u,c=Date.now())=>{n=c,r=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),f=c-n;f>=s?i(u,c):(r=u,o||(o=setTimeout(()=>{o=null,i(r)},s-f)))},()=>r&&i(r)]}const Ss=(e,t,n=3)=>{let s=0;const r=Mp(50,250);return $p(o=>{const i=o.loaded,l=o.lengthComputable?o.total:void 0,a=i-s,u=r(a),c=i<=l;s=i;const f={loaded:i,total:l,progress:l?i/l:void 0,bytes:a,rate:u||void 0,estimated:u&&l&&c?(l-i)/u:void 0,event:o,lengthComputable:l!=null,[t?"download":"upload"]:!0};e(f)},n)},bi=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},_i=e=>(...t)=>_.asap(()=>e(...t)),Lp=$e.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,$e.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL($e.origin),$e.navigator&&/(msie|trident)/i.test($e.navigator.userAgent)):()=>!0,Ip=$e.hasStandardBrowserEnv?{write(e,t,n,s,r,o){const i=[e+"="+encodeURIComponent(t)];_.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),_.isString(s)&&i.push("path="+s),_.isString(r)&&i.push("domain="+r),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function kp(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Np(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ra(e,t,n){let s=!kp(t);return e&&(s||n==!1)?Np(e,t):t}const Si=e=>e instanceof ze?{...e}:e;function rn(e,t){t=t||{};const n={};function s(u,c,f,d){return _.isPlainObject(u)&&_.isPlainObject(c)?_.merge.call({caseless:d},u,c):_.isPlainObject(c)?_.merge({},c):_.isArray(c)?c.slice():c}function r(u,c,f,d){if(_.isUndefined(c)){if(!_.isUndefined(u))return s(void 0,u,f,d)}else return s(u,c,f,d)}function o(u,c){if(!_.isUndefined(c))return s(void 0,c)}function i(u,c){if(_.isUndefined(c)){if(!_.isUndefined(u))return s(void 0,u)}else return s(void 0,c)}function l(u,c,f){if(f in t)return s(u,c);if(f in e)return s(void 0,u)}const a={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:l,headers:(u,c,f)=>r(Si(u),Si(c),f,!0)};return _.forEach(Object.keys(Object.assign({},e,t)),function(c){const f=a[c]||r,d=f(e[c],t[c],c);_.isUndefined(d)&&f!==l||(n[c]=d)}),n}const Ca=e=>{const t=rn({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:o,headers:i,auth:l}=t;t.headers=i=ze.from(i),t.url=Sa(Ra(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&i.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):"")));let a;if(_.isFormData(n)){if($e.hasStandardBrowserEnv||$e.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((a=i.getContentType())!==!1){const[u,...c]=a?a.split(";").map(f=>f.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if($e.hasStandardBrowserEnv&&(s&&_.isFunction(s)&&(s=s(t)),s||s!==!1&&Lp(t.url))){const u=r&&o&&Ip.read(o);u&&i.set(r,u)}return t},jp=typeof XMLHttpRequest<"u",Fp=jp&&function(e){return new Promise(function(n,s){const r=Ca(e);let o=r.data;const i=ze.from(r.headers).normalize();let{responseType:l,onUploadProgress:a,onDownloadProgress:u}=r,c,f,d,p,v;function y(){p&&p(),v&&v(),r.cancelToken&&r.cancelToken.unsubscribe(c),r.signal&&r.signal.removeEventListener("abort",c)}let S=new XMLHttpRequest;S.open(r.method.toUpperCase(),r.url,!0),S.timeout=r.timeout;function R(){if(!S)return;const D=ze.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),I={data:!l||l==="text"||l==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:D,config:e,request:S};xa(function(P){n(P),y()},function(P){s(P),y()},I),S=null}"onloadend"in S?S.onloadend=R:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(R)},S.onabort=function(){S&&(s(new ee("Request aborted",ee.ECONNABORTED,e,S)),S=null)},S.onerror=function(){s(new ee("Network Error",ee.ERR_NETWORK,e,S)),S=null},S.ontimeout=function(){let O=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const I=r.transitional||wa;r.timeoutErrorMessage&&(O=r.timeoutErrorMessage),s(new ee(O,I.clarifyTimeoutError?ee.ETIMEDOUT:ee.ECONNABORTED,e,S)),S=null},o===void 0&&i.setContentType(null),"setRequestHeader"in S&&_.forEach(i.toJSON(),function(O,I){S.setRequestHeader(I,O)}),_.isUndefined(r.withCredentials)||(S.withCredentials=!!r.withCredentials),l&&l!=="json"&&(S.responseType=r.responseType),u&&([d,v]=Ss(u,!0),S.addEventListener("progress",d)),a&&S.upload&&([f,p]=Ss(a),S.upload.addEventListener("progress",f),S.upload.addEventListener("loadend",p)),(r.cancelToken||r.signal)&&(c=D=>{S&&(s(!D||D.type?new _n(null,e,S):D),S.abort(),S=null)},r.cancelToken&&r.cancelToken.subscribe(c),r.signal&&(r.signal.aborted?c():r.signal.addEventListener("abort",c)));const C=Dp(r.url);if(C&&$e.protocols.indexOf(C)===-1){s(new ee("Unsupported protocol "+C+":",ee.ERR_BAD_REQUEST,e));return}S.send(o||null)})},Hp=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const o=function(u){if(!r){r=!0,l();const c=u instanceof Error?u:this.reason;s.abort(c instanceof ee?c:new _n(c instanceof Error?c.message:c))}};let i=t&&setTimeout(()=>{i=null,o(new ee(`timeout ${t} of ms exceeded`,ee.ETIMEDOUT))},t);const l=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:a}=s;return a.unsubscribe=()=>_.asap(l),a}},Bp=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},Up=async function*(e,t){for await(const n of Vp(e))yield*Bp(n,t)},Vp=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},wi=(e,t,n,s)=>{const r=Up(e,t);let o=0,i,l=a=>{i||(i=!0,s&&s(a))};return new ReadableStream({async pull(a){try{const{done:u,value:c}=await r.next();if(u){l(),a.close();return}let f=c.byteLength;if(n){let d=o+=f;n(d)}a.enqueue(new Uint8Array(c))}catch(u){throw l(u),u}},cancel(a){return l(a),r.return()}},{highWaterMark:2})},zs=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Aa=zs&&typeof ReadableStream=="function",qp=zs&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Oa=(e,...t)=>{try{return!!e(...t)}catch{return!1}},zp=Aa&&Oa(()=>{let e=!1;const t=new Request($e.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ti=64*1024,Fr=Aa&&Oa(()=>_.isReadableStream(new Response("").body)),ws={stream:Fr&&(e=>e.body)};zs&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!ws[t]&&(ws[t]=_.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new ee(`Response type '${t}' is not supported`,ee.ERR_NOT_SUPPORT,s)})})})(new Response);const Kp=async e=>{if(e==null)return 0;if(_.isBlob(e))return e.size;if(_.isSpecCompliantForm(e))return(await new Request($e.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(_.isArrayBufferView(e)||_.isArrayBuffer(e))return e.byteLength;if(_.isURLSearchParams(e)&&(e=e+""),_.isString(e))return(await qp(e)).byteLength},Wp=async(e,t)=>{const n=_.toFiniteNumber(e.getContentLength());return n??Kp(t)},Gp=zs&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:o,timeout:i,onDownloadProgress:l,onUploadProgress:a,responseType:u,headers:c,withCredentials:f="same-origin",fetchOptions:d}=Ca(e);u=u?(u+"").toLowerCase():"text";let p=Hp([r,o&&o.toAbortSignal()],i),v;const y=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let S;try{if(a&&zp&&n!=="get"&&n!=="head"&&(S=await Wp(c,s))!==0){let I=new Request(t,{method:"POST",body:s,duplex:"half"}),M;if(_.isFormData(s)&&(M=I.headers.get("content-type"))&&c.setContentType(M),I.body){const[P,U]=bi(S,Ss(_i(a)));s=wi(I.body,Ti,P,U)}}_.isString(f)||(f=f?"include":"omit");const R="credentials"in Request.prototype;v=new Request(t,{...d,signal:p,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:s,duplex:"half",credentials:R?f:void 0});let C=await fetch(v);const D=Fr&&(u==="stream"||u==="response");if(Fr&&(l||D&&y)){const I={};["status","statusText","headers"].forEach(V=>{I[V]=C[V]});const M=_.toFiniteNumber(C.headers.get("content-length")),[P,U]=l&&bi(M,Ss(_i(l),!0))||[];C=new Response(wi(C.body,Ti,P,()=>{U&&U(),y&&y()}),I)}u=u||"text";let O=await ws[_.findKey(ws,u)||"text"](C,e);return!D&&y&&y(),await new Promise((I,M)=>{xa(I,M,{data:O,headers:ze.from(C.headers),status:C.status,statusText:C.statusText,config:e,request:v})})}catch(R){throw y&&y(),R&&R.name==="TypeError"&&/Load failed|fetch/i.test(R.message)?Object.assign(new ee("Network Error",ee.ERR_NETWORK,e,v),{cause:R.cause||R}):ee.from(R,R&&R.code,e,v)}}),Hr={http:cp,xhr:Fp,fetch:Gp};_.forEach(Hr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ei=e=>`- ${e}`,Jp=e=>_.isFunction(e)||e===null||e===!1,Pa={getAdapter:e=>{e=_.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let o=0;o<t;o++){n=e[o];let i;if(s=n,!Jp(n)&&(s=Hr[(i=String(n)).toLowerCase()],s===void 0))throw new ee(`Unknown adapter '${i}'`);if(s)break;r[i||"#"+o]=s}if(!s){const o=Object.entries(r).map(([l,a])=>`adapter ${l} `+(a===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Ei).join(`
`):" "+Ei(o[0]):"as no adapter specified";throw new ee("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return s},adapters:Hr};function dr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new _n(null,e)}function xi(e){return dr(e),e.headers=ze.from(e.headers),e.data=fr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Pa.getAdapter(e.adapter||ts.adapter)(e).then(function(s){return dr(e),s.data=fr.call(e,e.transformResponse,s),s.headers=ze.from(s.headers),s},function(s){return Ea(s)||(dr(e),s&&s.response&&(s.response.data=fr.call(e,e.transformResponse,s.response),s.response.headers=ze.from(s.response.headers))),Promise.reject(s)})}const Da="1.9.0",Ks={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Ks[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const Ri={};Ks.transitional=function(t,n,s){function r(o,i){return"[Axios v"+Da+"] Transitional option '"+o+"'"+i+(s?". "+s:"")}return(o,i,l)=>{if(t===!1)throw new ee(r(i," has been removed"+(n?" in "+n:"")),ee.ERR_DEPRECATED);return n&&!Ri[i]&&(Ri[i]=!0,console.warn(r(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,l):!0}};Ks.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function Yp(e,t,n){if(typeof e!="object")throw new ee("options must be an object",ee.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const o=s[r],i=t[o];if(i){const l=e[o],a=l===void 0||i(l,o,e);if(a!==!0)throw new ee("option "+o+" must be "+a,ee.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new ee("Unknown option "+o,ee.ERR_BAD_OPTION)}}const ds={assertOptions:Yp,validators:Ks},pt=ds.validators;let nn=class{constructor(t){this.defaults=t||{},this.interceptors={request:new vi,response:new vi}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const o=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?o&&!String(s.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+o):s.stack=o}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=rn(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:o}=n;s!==void 0&&ds.assertOptions(s,{silentJSONParsing:pt.transitional(pt.boolean),forcedJSONParsing:pt.transitional(pt.boolean),clarifyTimeoutError:pt.transitional(pt.boolean)},!1),r!=null&&(_.isFunction(r)?n.paramsSerializer={serialize:r}:ds.assertOptions(r,{encode:pt.function,serialize:pt.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),ds.assertOptions(n,{baseUrl:pt.spelling("baseURL"),withXsrfToken:pt.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&_.merge(o.common,o[n.method]);o&&_.forEach(["delete","get","head","post","put","patch","common"],v=>{delete o[v]}),n.headers=ze.concat(i,o);const l=[];let a=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(a=a&&y.synchronous,l.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let c,f=0,d;if(!a){const v=[xi.bind(this),void 0];for(v.unshift.apply(v,l),v.push.apply(v,u),d=v.length,c=Promise.resolve(n);f<d;)c=c.then(v[f++],v[f++]);return c}d=l.length;let p=n;for(f=0;f<d;){const v=l[f++],y=l[f++];try{p=v(p)}catch(S){y.call(this,S);break}}try{c=xi.call(this,p)}catch(v){return Promise.reject(v)}for(f=0,d=u.length;f<d;)c=c.then(u[f++],u[f++]);return c}getUri(t){t=rn(this.defaults,t);const n=Ra(t.baseURL,t.url,t.allowAbsoluteUrls);return Sa(n,t.params,t.paramsSerializer)}};_.forEach(["delete","get","head","options"],function(t){nn.prototype[t]=function(n,s){return this.request(rn(s||{},{method:t,url:n,data:(s||{}).data}))}});_.forEach(["post","put","patch"],function(t){function n(s){return function(o,i,l){return this.request(rn(l||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}nn.prototype[t]=n(),nn.prototype[t+"Form"]=n(!0)});let Qp=class Ma{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const s=this;this.promise.then(r=>{if(!s._listeners)return;let o=s._listeners.length;for(;o-- >0;)s._listeners[o](r);s._listeners=null}),this.promise.then=r=>{let o;const i=new Promise(l=>{s.subscribe(l),o=l}).then(r);return i.cancel=function(){s.unsubscribe(o)},i},t(function(o,i,l){s.reason||(s.reason=new _n(o,i,l),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ma(function(r){t=r}),cancel:t}}};function Xp(e){return function(n){return e.apply(null,n)}}function Zp(e){return _.isObject(e)&&e.isAxiosError===!0}const Br={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Br).forEach(([e,t])=>{Br[t]=e});function $a(e){const t=new nn(e),n=ca(nn.prototype.request,t);return _.extend(n,nn.prototype,t,{allOwnKeys:!0}),_.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return $a(rn(e,r))},n}const Te=$a(ts);Te.Axios=nn;Te.CanceledError=_n;Te.CancelToken=Qp;Te.isCancel=Ea;Te.VERSION=Da;Te.toFormData=qs;Te.AxiosError=ee;Te.Cancel=Te.CanceledError;Te.all=function(t){return Promise.all(t)};Te.spread=Xp;Te.isAxiosError=Zp;Te.mergeConfig=rn;Te.AxiosHeaders=ze;Te.formToJSON=e=>Ta(_.isHTMLForm(e)?new FormData(e):e);Te.getAdapter=Pa.getAdapter;Te.HttpStatusCode=Br;Te.default=Te;const{Axios:vg,AxiosError:yg,CanceledError:bg,isCancel:_g,CancelToken:Sg,VERSION:wg,all:Tg,Cancel:Eg,isAxiosError:xg,spread:Rg,toFormData:Cg,AxiosHeaders:Ag,HttpStatusCode:Og,formToJSON:Pg,getAdapter:Dg,mergeConfig:Mg}=Te,pr={E500:"服务暂时不可用，请稍后再试",E400:"请求无效，请检查输入",E404:"未找到请求的资源",E4001:"未找到指定的城市",E5001:"推荐服务暂时不可用，请稍后再试",E5002:"旅游规划服务暂时不可用，请稍后再试",E5003:"票务服务暂时不可用，请稍后再试",E5004:"外部服务暂时不可用，请稍后再试",E4010:"未找到符合条件的车票，请尝试修改目的地或日期",E4011:"日期范围无效，请选择有效的日期范围",E4012:"出发城市和目的地城市之间可能没有直达列车，请尝试选择其他城市",default:"操作失败，请稍后再试"};function eh(e){return e&&pr[e]||pr.default}class Fn extends Error{constructor(n,s,r,o){super(n);wn(this,"status");wn(this,"data");wn(this,"errorCode");wn(this,"isApiError");this.name="ApiError",this.status=s,this.data=r,this.errorCode=o,this.isApiError=!0}getUserFriendlyMessage(){return eh(this.errorCode)}}function th(e){if(e.response){const t=e.response.status,n=e.response.data,s=n==null?void 0:n.error_code;let r="操作失败，请稍后再试";switch(t){case 400:r="请求参数错误";break;case 401:r="未授权，请登录";break;case 403:r="拒绝访问";break;case 404:r="请求的资源不存在";break;case 500:r="服务暂时不可用，请稍后再试";break;default:r="请求失败"}return n&&typeof n.message=="string"&&console.error(`API错误: ${n.message}`),new Fn(r,t,n,s)}else return e.request?new Fn("无法连接到服务器，请检查网络连接",0):new Fn("请求配置错误",0)}const nh={baseURL:"http://47.122.74.81:8000/api/v1",timeout:3e5,headers:{"Content-Type":"application/json",Accept:"application/json"}},Qt=Te.create(nh);Qt.interceptors.request.use(e=>{const t=localStorage.getItem("auth_token");t&&(e.headers.Authorization=`Bearer ${t}`);const n=localStorage.getItem("openrouter_api_key");n&&(e.headers["X-OpenRouter-API-Key"]=n);const s=localStorage.getItem("openrouter_default_model"),r=localStorage.getItem("openrouter_city_model"),o=localStorage.getItem("openrouter_travel_guide_model");return s&&(e.headers["X-OpenRouter-Default-Model"]=s),r&&(e.headers["X-OpenRouter-City-Model"]=r),o&&(e.headers["X-OpenRouter-Travel-Guide-Model"]=o),e},e=>Promise.reject(e));Qt.interceptors.response.use(e=>e,e=>Promise.reject(th(e)));const co={get:(e,t,n)=>Qt.get(e,{...n,params:t}),post:(e,t,n)=>Qt.post(e,t,n),put:(e,t,n)=>Qt.put(e,t,n),patch:(e,t,n)=>Qt.patch(e,t,n),delete:(e,t)=>Qt.delete(e,t)},sh={logToConsole:!0,showNotification:!0,title:"操作失败",duration:5e3};function Ts(e,t={}){const n={...sh,...t},s=e instanceof Fn?e:new Fn(e instanceof Error?e.message:"未知错误",500),r=n.customMessage||s.getUserFriendlyMessage();n.logToConsole&&console.error("API错误:",s),n.showNotification&&gn(r,n.title)}const rh={async getRecommendedCities(e){try{return(await co.post("/cities/recommend",e)).data}catch(t){return Ts(t,{customMessage:"获取推荐城市失败，请稍后再试"}),[]}}};function oh(e){return{id:e.id,name:e.name,description:e.description,rating:e.rating,cost:e.cost,recommendedStay:e.recommendedStay,imageUrl:e.imageUrl,events:e.events}}const Ci=[{id:"tokyo",name:"日本东京",description:"东京是一座充满活力的城市，融合了传统文化与现代科技。这里有美食、购物、历史景点和先进科技，适合各类旅行者。",rating:"4.8",cost:"中等",recommendedStay:"5-7 天",imageUrl:"https://images.unsplash.com/photo-1503899036084-c55cdd92da26?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{id:"chiangmai",name:"泰国清迈",description:"清迈是泰国北部的文化中心，以其古老的寺庙、传统手工艺和美食而闻名。这里的生活成本较低，是背包客和数字游民的热门目的地。",rating:"4.6",cost:"低",recommendedStay:"3-5 天",imageUrl:"https://images.unsplash.com/photo-1528181304800-259b08848526?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{id:"danang",name:"越南岘港",description:"岘港拥有美丽的海滩、独特的建筑和丰富的文化遗产。这里的物价较为亲民，是越南中部的旅游热点，适合寻求海滩度假的旅行者。",rating:"4.5",cost:"低",recommendedStay:"3-4 天",imageUrl:"https://images.unsplash.com/photo-1559592413-7cec4d0cae2b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"},{id:"penang",name:"马来西亚槟城",description:"槟城以其殖民地建筑、街头艺术和美食而著名。这座城市融合了马来、中国和印度文化，是一个多元文化的熔炉，特别适合美食爱好者。",rating:"4.7",cost:"低至中等",recommendedStay:"2-4 天",imageUrl:"https://images.unsplash.com/photo-1567157577867-05ccb1388e66?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=774&q=80"}];function ih(e={}){const{onSelect:t,useApi:n=!1,position:s="杭州"}=e,r=z(!1),o=Ct([...Ci]),i=Lr({onSelect:f=>{t&&typeof f=="string"&&t(f)}}),l=Ir({hideScrollbar:!1}),a=async f=>{if(!n)return;const d=f||s;console.log(`获取推荐城市，出发城市: ${d}`),r.value=!0;try{const p=await rh.getRecommendedCities({position:d});o.splice(0,o.length),p.map(oh).forEach(v=>{o.push(v)})}catch(p){console.error("获取推荐城市失败:",p),o.length===0&&Ci.forEach(v=>{o.push(v)})}finally{r.value=!1}},u=f=>i.selectItem(f),c=f=>o.find(d=>d.name===f);return{selectedDestination:i.selectedItems,destinationList:o,isLoading:r,selectDestination:u,getDestinationDetails:c,fetchDestinations:a,...l}}const lh={key:0,class:"loading-container"},ah={key:1,class:"item-list"},ch=["onClick"],uh={class:"item-content"},fh={class:"item-title"},dh={class:"item-description"},ph={class:"item-meta"},hh={key:0,class:"item-events"},mh={key:2,class:"empty-state"},gh=At({__name:"DestinationSelectionView",props:{useApi:{type:Boolean},position:{}},emits:["selectDestination"],setup(e,{expose:t,emit:n}){const s=e,r=n,{selectDestination:o,containerRef:i,destinationList:l,isLoading:a,fetchDestinations:u}=ih({onSelect:f=>{r("selectDestination",f)},useApi:s.useApi??!0,position:s.position??"杭州"}),c=f=>{o(f)};return t({fetchDestinations:u}),(f,d)=>(Z(),ne("div",{class:"content-page active",id:"step1-content",ref_key:"containerRef",ref:i},[B(a)?(Z(),ne("div",lh,d[1]||(d[1]=[g("div",{class:"loading-spinner"},[g("div",{class:"spinner-circle"}),g("div",{class:"spinner-circle-inner"})],-1),g("div",{class:"loading-text"},"正在获取推荐目的地...",-1)]))):(Z(),ne("div",ah,[(Z(!0),ne(Se,null,Et(B(l),p=>(Z(),ne("div",{key:p.id,class:"list-item",onClick:v=>c(p.name)},[g("div",uh,[g("div",null,[g("div",fh,Y(p.name),1),g("div",dh,Y(p.description),1)]),g("div",ph,[g("span",null,"⭐ "+Y(p.rating),1),g("span",null,"💰 "+Y(p.cost),1),g("span",null,"🕒 建议停留 "+Y(p.recommendedStay),1)]),p.events&&p.events.length>0?(Z(),ne("div",hh,[d[2]||(d[2]=g("div",{class:"events-title"},"当地活动:",-1)),(Z(!0),ne(Se,null,Et(p.events,(v,y)=>(Z(),ne("div",{key:y,class:"event-item"},[g("span",null,"🎭 "+Y(v.name),1),g("span",null,"⏰ "+Y(v.time),1),g("span",null,"💲 "+Y(v.price),1)]))),128))])):Rt("",!0)])],8,ch))),128))])),!B(a)&&B(l).length===0?(Z(),ne("div",mh,[d[3]||(d[3]=g("div",{class:"empty-icon"},"🏙️",-1)),d[4]||(d[4]=g("div",{class:"empty-text"},"暂无推荐目的地",-1)),g("button",{class:"refresh-btn",onClick:d[0]||(d[0]=()=>B(u)())}," 重新获取 ")])):Rt("",!0)],512))}}),vh={async searchTickets(e){try{const t=await co.post("/tickets/search",e),n=t.data.departureTickets&&t.data.departureTickets.length>0,s=t.data.returnTickets&&t.data.returnTickets.length>0,r=t.data.tickets&&t.data.tickets.length>0;return e.return_date&&!s&&console.warn("未找到返程车票"),!n&&!s&&!r&&Ts(new Error("未找到车票"),{customMessage:`未找到从 ${e.departure_city} 到 ${e.city_name} 的车票，请尝试修改目的地或日期`,title:"未找到车票",duration:5e3}),t.data}catch(t){return Ts(t,{customMessage:"搜索车票失败，请尝试修改目的地或日期",title:"搜索车票失败",duration:5e3}),{departureTickets:[],returnTickets:[],availableDates:[],tickets:[]}}}};function Ai(e){return{商务座:"商务",特等座:"特等",一等座:"一等",二等座:"二等",高级软卧:"高软",高级动卧:"高动",软卧:"软卧",硬卧:"硬卧",软座:"软座",硬座:"硬座",无座:"无座",二等包座:"包座",优选一等座:"优选",动卧:"动卧",一等卧:"一卧",二等卧:"二卧"}[e]||e}const yh=()=>{const e=[],t=new Date,n=["周日","周一","周二","周三","周四","周五","周六"];for(let s=0;s<15;s++){const r=new Date(t);r.setDate(t.getDate()+s);const o=r.toISOString().split("T")[0],i=r.getDate().toString(),l=n[r.getDay()];e.push({date:o,day:i,weekday:l})}return e},Es=yh(),Hn=z([...Es]),Be=z([]),Ze=z([]),xt=z(""),Nt=z(""),Ur=z(!1);async function Jt(e,t,n,s="杭州"){if(console.log(`开始获取车票数据: 目的地=${e}, 出发城市=${s}, 出发日期=${t}, 返回日期=${n||"无"}`),!e||!t){console.error("城市名称或出发日期为空，无法获取车票数据");return}Ur.value=!0,(()=>{document.querySelectorAll(".train-list-left .train-item").forEach(l=>{l.classList.remove("selected")}),document.querySelectorAll(".train-list:not(.train-list-left) .train-item").forEach(l=>{l.classList.remove("selected")})})();try{console.log("调用 ticketService.searchTickets API...");const o={city_name:e,departure_date:t,return_date:n,departure_city:s};console.log("请求参数:",o);const i=await vh.searchTickets(o);return console.log("API 响应:",i),i.availableDates&&i.availableDates.length>0?(console.log(`使用API返回的日期数据: ${i.availableDates.length} 个日期`),Hn.value=i.availableDates.map(l=>({date:l.date,day:l.day,weekday:l.weekday}))):(console.log("API 没有返回日期数据，使用默认日期生成逻辑"),Hn.value=[...Es]),i.departureTickets&&i.departureTickets.length>0?(console.log(`更新去程车票: ${i.departureTickets.length} 张`),Be.value=i.departureTickets,Be.value.length>0&&setTimeout(()=>{const l=Be.value[0].id,a=document.querySelector(`.train-list-left .train-item[data-train-id="${l}"]`);a&&(a.classList.add("selected"),xt.value=l,console.log(`默认选择去程车票: ${l}`))},300)):i.tickets&&i.tickets.length>0?(console.log(`使用旧版API返回的车票数据: ${i.tickets.length} 张`),Be.value=i.tickets,Be.value.length>0&&setTimeout(()=>{const l=Be.value[0].id,a=document.querySelector(`.train-list-left .train-item[data-train-id="${l}"]`);a&&(a.classList.add("selected"),xt.value=l,console.log(`默认选择去程车票: ${l}`))},300)):(console.warn("API 返回的去程车票为空"),Be.value=[],Be.value.length>0&&setTimeout(()=>{const l=Be.value[0].id,a=document.querySelector(`.train-list-left .train-item[data-train-id="${l}"]`);a&&(a.classList.add("selected"),xt.value=l,console.log(`默认选择去程车票: ${l}`))},300)),i.returnTickets&&i.returnTickets.length>0?(console.log(`更新返程车票: ${i.returnTickets.length} 张`),Ze.value=i.returnTickets,Ze.value.length>0&&setTimeout(()=>{const l=Ze.value[0].id,a=document.querySelector(`.train-list:not(.train-list-left) .train-item[data-train-id="${l}"]`);a&&(a.classList.add("selected"),Nt.value=l,console.log(`默认选择返程车票: ${l}`))},300)):n?(console.warn("API 返回的返程车票为空"),Ze.value=[],Ze.value.length>0&&setTimeout(()=>{const l=Ze.value[0].id,a=document.querySelector(`.train-list:not(.train-list-left) .train-item[data-train-id="${l}"]`);a&&(a.classList.add("selected"),Nt.value=l,console.log(`默认选择返程车票: ${l}`))},300)):(console.log("没有返程日期，清空返程车票"),Ze.value=[]),console.log("车票数据获取成功"),i}catch(o){console.error("获取车票数据失败:",o),console.log("使用默认日期数据，车票数据为空"),Hn.value=[...Es],Be.value=[],Ze.value=[]}finally{Ur.value=!1,console.log("车票数据加载完成")}}function bh(e={}){const{cityName:t="上海",departureCity:n="杭州"}=e,s=z([]),r=z(null),o=z(null),i=z(null),l=z(t),a=z(n),u=(y,S)=>{if(!r.value)return;const R=r.value.querySelectorAll(".date-item"),C=new Date(y),D=new Date(S);R.forEach(O=>{const I=new Date(O.getAttribute("data-date")||"");O.classList.remove("in-range"),I>C&&I<D&&O.classList.add("in-range"),I.getTime()===C.getTime()&&(O.classList.add("selected"),O.classList.add("range-start")),I.getTime()===D.getTime()&&(O.classList.add("selected"),O.classList.add("range-end"))})},c=(y,S)=>{if(!o.value||!i.value)return;const R=new Date(y),C=new Date(S),D=I=>{const M=I.getMonth()+1,P=I.getDate();return`${M}月${P}日`};o.value.textContent=`去程 (${D(R)})`,i.value.textContent=`返程 (${D(C)})`,document.querySelectorAll(".train-item").forEach(I=>{I.style.opacity="0.5",setTimeout(()=>{I.style.opacity="1"},300)}),Jt(l.value,y,S,a.value)},f=()=>{if(!r.value)return;s.value=[],r.value.querySelectorAll(".date-item").forEach(S=>{S.classList.remove("selected"),S.classList.remove("range-start"),S.classList.remove("range-end"),S.classList.remove("in-range")})};return{selectedDates:s,dateContainerRef:r,goTitleRef:o,returnTitleRef:i,selectedCity:l,selectedDepartureCity:a,markDateRange:u,updateTrainDisplay:c,clearDateSelections:f,handleDateClick:y=>{const S=y.currentTarget,R=S.getAttribute("data-date")||"";return s.value.length===2&&f(),S.classList.add("selected"),s.value.push(R),s.value.length===1?(S.classList.add("range-start"),S.classList.add("range-end"),console.log(`单日期选择: ${R}，设置为往返同一天`),Jt(l.value,R,R,a.value),{startDate:R,endDate:R}):s.value.length===2?(S.classList.add("range-end"),s.value.sort(),u(s.value[0],s.value[1]),c(s.value[0],s.value[1]),{startDate:s.value[0],endDate:s.value[1]}):null},setCity:y=>{l.value=y,console.log(`设置目的地城市: ${y}`),s.value.length>0?s.value.length===1?(console.log(`单日期选择: ${s.value[0]}，设置为往返同一天`),Jt(y,s.value[0],s.value[0],a.value)):s.value.length===2&&(console.log(`日期范围: ${s.value[0]} 至 ${s.value[1]}`),Jt(y,s.value[0],s.value[1],a.value)):console.log("没有选择日期，不获取车票数据")},setDepartureCity:y=>{a.value=y,console.log(`设置出发城市: ${y}`),s.value.length>0?s.value.length===1?(console.log(`单日期选择: ${s.value[0]}，设置为往返同一天`),Jt(l.value,s.value[0],s.value[0],y)):s.value.length===2&&(console.log(`日期范围: ${s.value[0]} 至 ${s.value[1]}`),Jt(l.value,s.value[0],s.value[1],y)):console.log("没有选择日期，不获取车票数据")}}}function _h(e={}){const{onSelect:t}=e,n=Lr({onSelect:u=>{t&&typeof u=="string"&&t(u,!0)}}),s=Lr({onSelect:u=>{t&&typeof u=="string"&&t(u,!1)}}),r=Ir({hideScrollbar:!0}),o=Ir({hideScrollbar:!0}),i=r.containerRef,l=o.containerRef;return{goTrainListRef:i,returnTrainListRef:l,handleTrainClick:(u,c)=>{const f=u.currentTarget,d=f.getAttribute("data-train-id")||"",p=c?".train-list-left .train-item":".train-list:not(.train-list-left) .train-item";document.querySelectorAll(p).forEach(y=>{y.classList.remove("selected")}),c?(n.selectItem(d),xt.value=d):(s.selectItem(d),Nt.value=d),f.classList.add("selected")},goTrainSelection:n,returnTrainSelection:s,scrollToTop:r.scrollToTop,scrollToBottom:r.scrollToBottom,scrollToElement:r.scrollToElement,setScrollingEnabled:r.setScrollingEnabled,goScrollPosition:r.scrollPosition,goIsScrolling:r.isScrolling,returnScrollPosition:o.scrollPosition,returnIsScrolling:o.isScrolling}}function Sh(e,t){const{selectedDates:n,dateContainerRef:s,clearDateSelections:r,selectedCity:o,selectedDepartureCity:i}=e,{goTrainListRef:l,returnTrainListRef:a}=t;return{initializeComponent:(c,f)=>{c&&(o.value=c),f&&(i.value=f),r();const p=new Date().toISOString().split("T")[0];if(console.log(`初始化组件，当前日期: ${p}, 选中城市: ${o.value}`),s.value){let v=s.value.querySelector(`.date-item[data-date="${p}"]`);if(v||(console.log("找不到当前日期的日期项，使用第一个日期项"),v=s.value.querySelector(".date-item")),v){console.log(`选中日期项: ${v.getAttribute("data-date")}`),v.classList.add("selected"),v.classList.add("range-start"),v.classList.add("range-end");const y=v.getAttribute("data-date")||p;n.value.push(y),c&&f?(console.log(`获取初始车票数据: 目的地=${c}, 出发城市=${f}, 出发日期=${y}, 返回日期=${y}`),Jt(c,y,y,f)):(console.log("未提供明确的城市名称或出发城市，不调用车票查询接口"),Hn.value=[...Es],Be.value=[],Ze.value=[])}else console.warn("找不到任何日期项，无法初始化")}else console.warn("dateContainerRef.value 为空，无法初始化日期选择");if(l.value){const v=l.value.querySelector(".train-item");if(v){v.classList.add("selected");const y=v.getAttribute("data-train-id")||"";xt.value=y}}if(a.value){const v=a.value.querySelector(".train-item");if(v){v.classList.add("selected");const y=v.getAttribute("data-train-id")||"";Nt.value=y}}}}}const wh={class:"content-page",id:"step2-content"},Th={key:0,class:"loading-overlay"},Eh={class:"train-container"},xh={class:"train-list-container"},Rh={key:0,class:"no-tickets-message"},Ch=["data-train-id"],Ah={class:"train-item-header"},Oh={class:"train-time"},Ph={class:"train-stations"},Dh={class:"train-duration"},Mh={class:"train-time"},$h={class:"train-stations"},Lh={class:"train-info"},Ih={class:"train-number"},kh={class:"train-price"},Nh={class:"train-tags"},jh={class:"seat-name"},Fh={class:"seat-count"},Hh={key:0,class:"no-tickets-message"},Bh=["data-train-id"],Uh={class:"train-item-header"},Vh={class:"train-time"},qh={class:"train-stations"},zh={class:"train-duration"},Kh={class:"train-time"},Wh={class:"train-stations"},Gh={class:"train-info"},Jh={class:"train-number"},Yh={class:"train-price"},Qh={class:"train-tags"},Xh={class:"seat-name"},Zh={class:"seat-count"},em={class:"train-date-selector"},tm=["data-date"],nm={class:"date-day"},sm={class:"date-weekday"},rm=At({__name:"TimeSelectionView",props:{cityName:{},departureCity:{}},emits:["datesSelected"],setup(e,{expose:t,emit:n}){const s=e,r=n,o=bh({cityName:s.cityName||"上海",departureCity:s.departureCity||"杭州"}),i=_h({onSelect:(M,P)=>{console.log(`选择了${P?"去程":"返程"}火车: ${M}`)}}),{initializeComponent:l}=Sh(o,i);tn(()=>s.cityName,M=>{M&&o.setCity(M)}),tn(()=>s.departureCity,M=>{M&&o.setDepartureCity(M)}),on(()=>{console.log("TimeSelectionView 组件已挂载")});const{dateContainerRef:a,goTitleRef:u,returnTitleRef:c,handleDateClick:f}=o,{goTrainListRef:d,returnTrainListRef:p,handleTrainClick:v,goScrollPosition:y,returnScrollPosition:S}=i,R=M=>{f(M)},C=()=>{if(console.log("确认选择"),console.log("选中的去程车票:",xt.value),console.log("选中的返程车票:",Nt.value),o.selectedDates.value.length===0){console.warn("未选择日期，无法确认");return}const M=[...o.selectedDates.value];M.length===1?M.push(M[0]):M.length>2&&M.splice(2),M.sort(),console.log(`确认选择日期: ${M.join(" 至 ")}`),r("datesSelected",M)},D=()=>{d.value&&d.value.scrollTo({top:0,behavior:"smooth"})},O=()=>{p.value&&p.value.scrollTo({top:0,behavior:"smooth"})};return t({initializeComponent:l,getSelectedTrains:()=>{if(!xt.value&&!Nt.value)return console.log("没有选中的火车票"),null;let M=null;xt.value&&(M=Be.value.find(U=>U.id===xt.value));let P=null;return Nt.value&&(P=Ze.value.find(U=>U.id===Nt.value)),{outbound:M,return:P}}}),(M,P)=>(Z(),ne("div",wh,[B(Ur)?(Z(),ne("div",Th,P[2]||(P[2]=[g("div",{class:"loading-spinner"},[g("div",{class:"spinner-circle"}),g("div",{class:"spinner-circle-inner"})],-1),g("div",{class:"loading-text"},"正在获取车票数据...",-1)]))):Rt("",!0),g("div",Eh,[g("div",xh,[g("div",{class:"train-list train-list-left",ref_key:"goTrainListRef",ref:d},[g("h3",{ref_key:"goTitleRef",ref:u,style:{"margin-bottom":"15px",color:"#1a365d","text-align":"center"}}," 去程 ",512),B(Be).length===0?(Z(),ne("div",Rh,P[3]||(P[3]=[g("div",{class:"no-tickets-text"},"未找到符合条件的车票",-1),g("div",{class:"no-tickets-subtext"},"请尝试修改目的地或日期",-1)]))):(Z(!0),ne(Se,{key:1},Et(B(Be),U=>(Z(),ne("div",{key:U.id,class:"train-item","data-train-id":U.id,onClick:P[0]||(P[0]=V=>B(v)(V,!0))},[g("div",Ah,[g("div",null,[g("div",Oh,Y(U.departureTime),1),g("div",Ph,Y(U.departureStation),1)]),g("div",null,[g("div",Dh,Y(U.duration),1),P[4]||(P[4]=g("div",{style:{"text-align":"center","font-size":"18px",color:"#718096"}}," → ",-1))]),g("div",null,[g("div",Mh,Y(U.arrivalTime),1),g("div",$h,Y(U.arrivalStation),1)])]),g("div",Lh,[g("div",Ih,Y(U.trainNumber),1),g("div",kh,Y(U.price),1)]),g("div",Nh,[(Z(!0),ne(Se,null,Et(U.tags,(V,se)=>(Z(),ne("div",{key:se,class:"train-tag"},[g("span",jh,Y(B(Ai)(V.name)),1),g("span",Fh,Y(V.count>0?`${V.count}张`:"无票"),1)]))),128))])],8,Ch))),128)),g("div",{class:je(["back-to-top",{hidden:B(y)<=50}]),onClick:D}," ↑ ",2)],512),g("div",{class:"train-list",ref_key:"returnTrainListRef",ref:p},[g("h3",{ref_key:"returnTitleRef",ref:c,style:{"margin-bottom":"15px",color:"#1a365d","text-align":"center"}}," 返程 ",512),B(Ze).length===0?(Z(),ne("div",Hh,P[5]||(P[5]=[g("div",{class:"no-tickets-text"},"未找到符合条件的车票",-1),g("div",{class:"no-tickets-subtext"},"请尝试修改目的地或日期",-1)]))):(Z(!0),ne(Se,{key:1},Et(B(Ze),U=>(Z(),ne("div",{key:U.id,class:"train-item","data-train-id":U.id,onClick:P[1]||(P[1]=V=>B(v)(V,!1))},[g("div",Uh,[g("div",null,[g("div",Vh,Y(U.departureTime),1),g("div",qh,Y(U.departureStation),1)]),g("div",null,[g("div",zh,Y(U.duration),1),P[6]||(P[6]=g("div",{style:{"text-align":"center","font-size":"18px",color:"#718096"}}," → ",-1))]),g("div",null,[g("div",Kh,Y(U.arrivalTime),1),g("div",Wh,Y(U.arrivalStation),1)])]),g("div",Gh,[g("div",Jh,Y(U.trainNumber),1),g("div",Yh,Y(U.price),1)]),g("div",Qh,[(Z(!0),ne(Se,null,Et(U.tags,(V,se)=>(Z(),ne("div",{key:se,class:"train-tag"},[g("span",Xh,Y(B(Ai)(V.name)),1),g("span",Zh,Y(V.count>0?`${V.count}张`:"无票"),1)]))),128))])],8,Bh))),128)),g("div",{class:je(["back-to-top",{hidden:B(S)<=50}]),onClick:O}," ↑ ",2)],512)]),g("div",em,[P[8]||(P[8]=g("div",{class:"date-selector-header"},[g("div",{class:"date-title"},"选择日期")],-1)),g("div",{class:"date-selector-content",ref_key:"dateContainerRef",ref:a},[(Z(!0),ne(Se,null,Et(B(Hn),U=>(Z(),ne("div",{key:U.date,class:"date-item","data-date":U.date,onClick:R},[g("div",nm,Y(U.day),1),g("div",sm,Y(U.weekday),1)],8,tm))),128))],512),g("div",{class:"confirm-button-container"},[g("div",{class:"confirm-button",onClick:C},P[7]||(P[7]=[g("div",{class:"date-day"},"确认",-1),g("div",{class:"date-weekday"},"选择",-1)]))])])])]))}}),om={async generateGuide(e){try{return(await co.post("/travel-guides/generate-guide",e)).data}catch(t){return Ts(t,{customMessage:"生成旅游指南失败，请稍后再试"}),[]}}},Ce=Ct([{id:"transport-initial",type:"transportation",time:"07:00 - 08:00",icon:"✈️",duration:"1小时",detail:"从北京首都机场飞往上海虹桥机场"},{id:"shanghai-hotel",type:"attraction",time:"09:00 - 10:30",name:"上海迎宾馆",description:"上海迎宾馆是上海地标性建筑，融合了中西建筑风格，可以欣赏到独特的建筑艺术。",ticketPrice:"免费",recommendedStay:"1.5小时",imageUrl:"https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",position:"left"},{id:"transport-1",type:"transportation",time:"10:30 - 10:45",icon:"🚕",duration:"15分钟",detail:"打车从迎宾馆南门到外滩观光隧道入口"},{id:"the-bund",type:"attraction",time:"11:00 - 13:00",name:"外滩",description:"外滩是上海最著名的商业街区之一，汇集了各类商场、餐厅和历史建筑。",ticketPrice:"免费",recommendedStay:"2小时",imageUrl:"https://images.unsplash.com/photo-1474181487882-5abf3f0ba6c2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",position:"right"},{id:"transport-2",type:"transportation",time:"13:00 - 13:20",icon:"🚇",duration:"20分钟",detail:"地铁2号线从南京东路站到人民广场站"},{id:"howard-garden",type:"attraction",time:"13:30 - 15:00",name:"豪生花园",description:"豪生花园是上海最大的花园之一，园内植物种类丰富，环境幽雅，是休闲游玩的好去处。",ticketPrice:"¥50",recommendedStay:"1.5小时",imageUrl:"https://images.unsplash.com/photo-1585320806297-9794b3e4eeae?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1332&q=80",position:"left"},{id:"transport-3",type:"transportation",time:"15:00 - 15:10",icon:"🚶",duration:"10分钟",detail:"步行从豪生花园西门到田子坊北入口"},{id:"tianzifang",type:"attraction",time:"15:30 - 17:30",name:"田子坊",description:"田子坊是上海历史悠久的街区，保存了大量的石库门建筑，充满浓厚的文化氛围。",ticketPrice:"免费",recommendedStay:"2小时",imageUrl:"https://images.unsplash.com/photo-1598511726623-d3e9f2db00e9?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",position:"right"},{id:"transport-4",type:"transportation",time:"17:30 - 17:55",icon:"🚕",duration:"25分钟",detail:"打车从田子坊南门到外滩观景平台"},{id:"bund-night",type:"attraction",time:"18:30 - 20:30",name:"外滩夜景",description:"晚上的外滩灯火辨然，汇聚了世界各地的建筑风格，是欣赏上海夜景的最佳地点。",ticketPrice:"免费",recommendedStay:"2小时",imageUrl:"https://images.unsplash.com/photo-1536599424071-0b215a388ba7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",position:"left"}]),Pn=Ct([{date:"2023-07-15",number:1,label:"第一天"},{date:"2023-07-16",number:2,label:"第二天"},{date:"2023-07-17",number:3,label:"第三天"},{date:"2023-07-18",number:4,label:"第四天"},{date:"2023-07-19",number:5,label:"第五天"}]);async function Oi(e,t=2,n){console.log(`开始获取旅游指南数据: 城市=${e}, 天数=${t}`),n&&console.log(`火车票信息: 去程=${n.departureTrainNumber}, 返程=${n.returnTrainNumber}`);try{const s=n?{departure_train:n.departureTrainNumber,departure_time:n.departureTime,arrival_time:n.arrivalTime,return_train:n.returnTrainNumber,return_departure_time:n.returnDepartureTime,return_arrival_time:n.returnArrivalTime}:void 0,r=await om.generateGuide({city:e,days:t,trainInfo:s});if(lm(t),console.log(`成功获取旅游指南数据: ${r.length} 个项目`),r.length>0){if(Ce.splice(0,Ce.length),n&&n.departureTrainNumber){const o={id:`train-departure-${n.departureTrainNumber}`,type:"transportation",time:n.departureTime||"未知时间",icon:"🚄",duration:n.arrivalTime?`到达时间: ${n.arrivalTime}`:"未知时长",detail:`乘坐 ${n.departureTrainNumber} 次列车前往目的地`};r.unshift(o)}if(r.forEach(o=>{Ce.push(o)}),n&&n.returnTrainNumber){const o={id:`train-return-${n.returnTrainNumber}`,type:"transportation",time:n.returnDepartureTime||"未知时间",icon:"🚄",duration:n.returnArrivalTime?`到达时间: ${n.returnArrivalTime}`:"未知时长",detail:`乘坐 ${n.returnTrainNumber} 次列车返回出发地`};Ce.push(o)}return im(t),!0}else return console.warn("API返回的旅游指南数据为空"),!1}catch(s){return console.error("获取旅游指南数据失败:",s),!1}}function im(e){if(mt={},e===1){mt[0]=[...Ce];return}const t=Ce.find(o=>o.type==="transportation"&&o.id.startsWith("train-departure-")),n=Ce.find(o=>o.type==="transportation"&&o.id.startsWith("train-return-")),s=Ce.filter(o=>!(o.type==="transportation"&&(o.id.startsWith("train-departure-")||o.id.startsWith("train-return-")))),r=Math.ceil(s.length/e);for(let o=0;o<e;o++){const i=o*r,l=Math.min(i+r,s.length);if(i>=s.length){mt[o]=[];continue}mt[o]=s.slice(i,l),o===0&&t&&mt[o].unshift(t),o===e-1&&n&&mt[o].push(n),console.log(`第${o+1}天的行程: ${mt[o].length}个项目`)}}function lm(e){console.log(`更新日期数据，天数: ${e}`),Pn.splice(0,Pn.length);const t=new Date,n=Math.max(1,Math.min(e,15));for(let s=0;s<n;s++){const r=new Date(t);r.setDate(t.getDate()+s);const o=r.toISOString().split("T")[0],i=s+1,l=`第${i}天`;Pn.push({date:o,number:i,label:l})}console.log(`更新后的日期数据: ${Pn.length}天`)}let mt={0:[]};function am(){const e=z(null),t=z(!1),n=z("正在生成行程计划...");return mt[0].length===0&&Ce.length>0&&(mt[0]=[...Ce]),{timelineContentRef:e,updateItinerary:r=>{e.value&&(e.value.style.opacity="0.5",setTimeout(()=>{const o=parseInt(r)-1,i=mt[o]||[];i.length===0?(console.log(`第${r}天的行程尚未规划`),Ce.splice(0,Ce.length),Ce.push({id:`empty-day-${o}`,type:"attraction",time:"全天",name:"暂无行程",description:`第${r}天的行程尚未规划，请选择其他日期查看。`,ticketPrice:"无",recommendedStay:"无",imageUrl:"https://images.unsplash.com/photo-1522201949034-507737bce479?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",position:"left"})):(console.log(`显示第${r}天的行程: ${i.length}个项目`),Ce.splice(0,Ce.length),i.forEach(l=>{Ce.push(l)})),e.value.style.opacity="1"},300))},isLoading:t,loadingText:n}}function cm(){const e=s=>{s.classList.toggle("expanded");const r=s.querySelector(".expand-icon");r&&(s.classList.contains("expanded")?r.textContent="▲":r.textContent="▼")};return{toggleCardExpand:e,handleIconClick:(s,r)=>{s.stopPropagation(),e(r)},toggleTransportation:s=>{s.classList.toggle("expanded")}}}function um(){const e=z(null),t=z("1");return{daySelectorRef:e,selectedDay:t,handleDaySelect:(r,o)=>{t.value=r.toString(),o(r.toString())},isDaySelected:r=>t.value===r.toString()}}function fm(e,t,n){const{updateItinerary:s,isLoading:r,loadingText:o}=e,{daySelectorRef:i,selectedDay:l}=n;return{initializeComponent:async(u,c,f,d)=>{var p;o.value=`正在为您生成${u||"上海"}的行程计划...`,r.value=!0;try{const v=u||"上海";console.log(`初始化行程计划组件，城市: ${v}`);let y=2;if(c&&c.length===2){const C=new Date(c[0]),D=new Date(c[1]),O=Math.abs(D.getTime()-C.getTime());y=Math.ceil(O/(1e3*60*60*24))+1}const S=f?{departureTrainNumber:f.trainNumber,departureTime:f.departureTime,arrivalTime:f.arrivalTime,returnTrainNumber:d==null?void 0:d.trainNumber,returnDepartureTime:d==null?void 0:d.departureTime,returnArrivalTime:d==null?void 0:d.arrivalTime}:void 0;S&&console.log(`使用火车票信息生成行程: 去程=${S.departureTrainNumber}, 返程=${S.returnTrainNumber}`),console.log(`强制从API获取旅游指南数据: 城市=${v}, 天数=${y}`),await Oi(v,y,S)||(console.warn("获取旅游指南数据失败，使用默认数据"),Ce.length===0&&(console.log("没有现有数据，使用默认的上海数据"),await Oi("上海",2)))}catch(v){console.error("初始化组件时出错:",v)}finally{r.value=!1}if(i.value){const v=i.value.querySelector(".day-item");if(v){v.classList.add("selected");const y=((p=v.querySelector(".day-number"))==null?void 0:p.textContent)||"1";l.value=y,s(y)}}l.value="1",s("1")}}}const dm={class:"content-page",id:"step3-content"},pm={class:"itinerary-container"},hm={key:0,class:"loading-overlay"},mm={class:"loading-text"},gm={key:0,class:"timeline-item"},vm={class:"timeline-card-title"},ym={class:"timeline-card-content"},bm={class:"timeline-card-details"},_m={class:"timeline-card-meta"},Sm={class:"timeline-time"},wm={key:1,class:"transportation-item"},Tm={class:"transportation-header"},Em={class:"transportation-icon"},xm={class:"transportation-time"},Rm={class:"transportation-detail"},Cm={class:"day-selector"},Am=["data-date","onClick"],Om={class:"day-number"},Pm={class:"day-label"},Dm=At({__name:"ItineraryPlanView",props:{cityName:{},selectedDates:{},departureTrainInfo:{},returnTrainInfo:{}},setup(e,{expose:t}){const n=e,s=am(),r=cm(),o=um(),{timelineContentRef:i,updateItinerary:l,isLoading:a,loadingText:u}=s,{toggleCardExpand:c,handleIconClick:f,toggleTransportation:d}=r,{daySelectorRef:p,handleDaySelect:v,isDaySelected:y}=o,{initializeComponent:S}=fm(s,r,o),R=I=>{v(I,l)},C=I=>{const M=I.currentTarget;c(M)},D=(I,M)=>{f(I,M)},O=I=>{const M=I.currentTarget;d(M)};return on(()=>{console.log("ItineraryPlanView 组件已挂载，等待父组件初始化"),n.departureTrainInfo&&console.log(`去程火车信息: ${n.departureTrainInfo.trainNumber}, 出发时间: ${n.departureTrainInfo.departureTime}, 到达时间: ${n.departureTrainInfo.arrivalTime}`),n.returnTrainInfo&&console.log(`返程火车信息: ${n.returnTrainInfo.trainNumber}, 出发时间: ${n.returnTrainInfo.departureTime}, 到达时间: ${n.returnTrainInfo.arrivalTime}`)}),t({initializeComponent:S}),(I,M)=>(Z(),ne("div",dm,[g("div",pm,[B(a)?(Z(),ne("div",hm,[M[1]||(M[1]=g("div",{class:"loading-spinner"},[g("div",{class:"spinner-circle"}),g("div",{class:"spinner-circle-inner"})],-1)),g("div",mm,Y(B(u)),1)])):Rt("",!0),g("div",{class:je(["timeline-container",{loading:B(a)}])},[M[4]||(M[4]=g("div",{class:"timeline"},null,-1)),g("div",{class:"timeline-content",ref_key:"timelineContentRef",ref:i},[(Z(!0),ne(Se,null,Et(B(Ce),P=>(Z(),ne(Se,{key:P.id},[P.type==="attraction"?(Z(),ne("div",gm,[M[2]||(M[2]=g("div",{class:"timeline-dot"},null,-1)),g("div",{class:je(["timeline-card",P.position]),onDblclick:C},[g("div",vm,[g("span",null,Y(P.name),1),g("span",{class:"expand-icon",onClick:M[0]||(M[0]=U=>D(U,U.target.closest(".timeline-card")))},"▼")]),g("div",ym,Y(P.description),1),g("div",bm,[g("div",_m,[g("span",null,"门票: "+Y(P.ticketPrice),1),g("span",null,"推荐停留: "+Y(P.recommendedStay),1)])])],34),g("div",Sm,Y(P.time),1)])):P.type==="transportation"?(Z(),ne("div",wm,[M[3]||(M[3]=g("div",{class:"transportation-dot"},null,-1)),g("div",{class:"transportation",onClick:O},[g("div",Tm,[g("span",Em,Y(P.icon),1),g("span",xm,Y(P.duration),1)]),g("span",Rm,Y(P.detail),1)])])):Rt("",!0)],64))),128))],512)],2),g("div",Cm,[M[5]||(M[5]=g("div",{class:"day-selector-header"},[g("div",{class:"day-title"},"行程日期")],-1)),g("div",{class:"day-selector-content",ref_key:"daySelectorRef",ref:p},[(Z(!0),ne(Se,null,Et(B(Pn),P=>(Z(),ne("div",{key:P.date,class:je(["day-item",{selected:B(y)(P.number)}]),"data-date":P.date,onClick:()=>R(P.number)},[g("div",Om,Y(P.number),1),g("div",Pm,Y(P.label),1)],10,Am))),128))],512)])])]))}});function Mm(e=0,t=3){const n=z(e),s=z(Array(t).fill(0).map((a,u)=>({active:u===e,inProgress:u===e}))),r=a=>{if(a<0||a>=t){console.warn(`步骤索引 ${a} 超出范围 [0, ${t-1}]`);return}n.value=a,s.value=s.value.map((u,c)=>c===a?{active:!0,inProgress:!0}:c<a?{active:!0,inProgress:!1}:{active:!1,inProgress:!1})};return{currentStep:n,stepStates:s,switchStep:r,nextStep:()=>{n.value<t-1&&r(n.value+1)},prevStep:()=>{n.value>0&&r(n.value-1)},resetSteps:()=>{r(e)}}}function $m(e){const{mainRef:t,headerRef:n}=e,s=z(null),r=z(null),o=z(null),i=z(!1),l=()=>{if(i.value||!(t!=null&&t.value)||!s.value||!r.value||!(n!=null&&n.value))return;const f=t.value.offsetHeight,d=s.value.offsetHeight,p=r.value.offsetHeight,v=n.value.offsetHeight,y=d+p,S=f-v;let R=Math.max(0,(S-y)/2-2*v);const C=Math.min(S*.3,150);R=Math.min(R,C),(isNaN(R)||!isFinite(R))&&(R=0),s.value.style.transform=`translateY(${R}px)`,r.value.style.transform=`translateY(${R}px)`},a=()=>{i.value||!o.value||!s.value||!r.value||(o.value.style.display="block",setTimeout(()=>{o.value&&(o.value.style.opacity="1",o.value.style.transform="translateY(0)",s.value&&r.value&&(s.value.style.transform="translateY(0)",r.value.style.transform="translateY(0)",r.value.style.position="relative",r.value.style.zIndex="100"),i.value=!0)},50))},u=()=>{!i.value||!o.value||(o.value.style.opacity="0",o.value.style.transform="translateY(20px)",i.value=!1,setTimeout(()=>{o.value&&(o.value.style.display="none",r.value&&(r.value.style.position="",r.value.style.zIndex="",l()))},800))},c=f=>{const d=document.querySelector(".inbox-content");d&&(d.style.overflowY=f?"auto":"hidden")};return on(()=>{l(),window.addEventListener("resize",l)}),Xn(()=>{window.removeEventListener("resize",l)}),{inboxVisible:i,mainTitleRef:s,originInputContainerRef:r,inboxContainerRef:o,centerElements:l,showInbox:a,hideInbox:u,setContentScrolling:c}}function Lm(e,t){const n=z([]),s=z("");return{selectedDates:n,selectedDestination:s,handleDestinationSelect:l=>{s.value=l,console.log(`选择了目的地: ${l}`),e&&e(l)},handleDatesSelected:l=>{n.value=l,console.log(`选择了日期: ${l.join(" 至 ")}`),t&&t(l)},clearSelections:()=>{n.value=[],s.value=""}}}function Im(){const e=z(null),t=z(null);return{timeSelectionRef:e,itineraryPlanRef:t,initTimeSelectionComponent:(r,o)=>{e.value?setTimeout(()=>{var i,l,a;console.log("调用 TimeSelectionView 组件的 initializeComponent 方法"),r?(console.log(`传递目的地城市名称: ${r}`),o?(console.log(`传递出发城市名称: ${o}`),(i=e.value)==null||i.initializeComponent(r,o)):(console.log("没有传递出发城市名称，使用默认值"),(l=e.value)==null||l.initializeComponent(r))):(console.log("没有传递城市名称，使用默认值"),(a=e.value)==null||a.initializeComponent())},100):console.warn("timeSelectionRef.value 为空，无法初始化组件")},initItineraryPlanComponent:(r,o,i,l)=>{t.value?setTimeout(()=>{var a,u;r&&o?(console.log(`初始化行程计划组件，城市: ${r}, 日期: ${o.join(" 至 ")}`),i&&console.log(`去程火车信息: ${i.trainNumber}, 出发时间: ${i.departureTime}, 到达时间: ${i.arrivalTime}`),l&&console.log(`返程火车信息: ${l.trainNumber}, 出发时间: ${l.departureTime}, 到达时间: ${l.arrivalTime}`),(a=t.value)==null||a.initializeComponent(r,o,i,l)):(console.log("没有传递城市名称或日期，使用默认值"),(u=t.value)==null||u.initializeComponent())},100):console.warn("itineraryPlanRef.value 为空，无法初始化组件")}}}let hr=null;async function km(){if(hr)return hr;try{const e=await fetch("/data/TrainAllStation.json");if(!e.ok)throw new Error(`Failed to load city data: ${e.status} ${e.statusText}`);const t=await e.json();return hr=t,t}catch(e){return console.error("Error loading city data:",e),[]}}async function Nm(e){return!e||e.trim()===""?!1:(await km()).some(n=>n.Name===e.trim())}const jm={class:"origin-input"},Fm={class:"inbox-header"},Hm={class:"steps-container"},Bm={class:"buttons-container"},Um={class:"inbox-content"},Vm={key:0,class:"loading-overlay"},qm={class:"loading-text"},zm=At({__name:"MainView",setup(e){const t=aa(),n=tt("mainRef"),s=tt("headerRef"),r=z(!1),o=z("加载中..."),i=z("杭州"),l=z(void 0),a=z(void 0),u=(Q="加载中...")=>{o.value=Q,r.value=!0},c=()=>{r.value=!1},f=Mm(0,3),{currentStep:d,stepStates:p,switchStep:v}=f,y=z(0),S=z(!1),R=$m({mainRef:n,headerRef:s}),{mainTitleRef:C,originInputContainerRef:D,inboxContainerRef:O,showInbox:I,hideInbox:M,setContentScrolling:P}=R,U=z(null),V=()=>{const Q=localStorage.getItem("openrouter_api_key");return!!Q&&Q.trim()!==""},se=async()=>{if(!i.value||i.value.trim()===""){gn("请输入出发城市名称","城市验证失败");return}if(!await Nm(i.value)){gn(`"${i.value}" 不是有效的城市名称，或者没有火车站，请输入正确的城市名称`,"城市验证失败");return}if(!V()){Gl("您尚未配置OpenRouter API Key，请先在设置页面中配置API密钥","API密钥未配置"),t.push("/settings");return}I(),S.value=!1,_e(0),setTimeout(()=>{console.log(`使用最新的出发城市: ${i.value} 刷新数据`),ke()},100)},pe=Im(),{timeSelectionRef:H,itineraryPlanRef:oe,initTimeSelectionComponent:Ee,initItineraryPlanComponent:Ie}=pe,fe=Lm(Q=>{_e(1)},Q=>{_e(2)}),{handleDestinationSelect:ae,handleDatesSelected:ie}=fe,st=Q=>{console.log(`选择了新的目的地: ${Q}，标记需要刷新行程计划`),S.value=!0,ae(Q)},at=Q=>{console.log(`选择了新的日期: ${Q.join(" 至 ")}，标记需要刷新行程计划`),S.value=!0,ie(Q)},ke=()=>{const Q=d.value;let te="";if(Q===0?te="正在刷新目的地选择...":Q===1?te="正在刷新时间选择...":Q===2&&(te="正在刷新行程计划..."),u(te),Q===0)console.log("刷新目的地选择页面"),console.log(`当前出发城市: ${i.value}`),U.value&&U.value.fetchDestinations(i.value),P(!0),console.log("目的地选择页面：启用滚动");else if(Q===1){console.log("刷新时间选择页面，初始化组件");const be=fe.selectedDestination.value;console.log(`当前选择的目的地城市: ${be||"未选择"}`),console.log(`当前出发城市: ${i.value}`),be?Ee(be,i.value):console.log("没有选择目的地城市，不初始化时间选择组件"),P(!1),console.log("时间选择页面：禁用滚动")}else if(Q===2){console.log("刷新行程计划页面，初始化组件");let be=fe.selectedDestination.value,xe=fe.selectedDates.value;if(be||(be="上海",console.log("没有选择城市，使用默认城市：上海")),!xe||xe.length===0){const w=new Date,j=new Date(w);j.setDate(w.getDate()+1),xe=[w.toISOString().split("T")[0],j.toISOString().split("T")[0]],console.log(`没有选择日期，使用默认日期：${xe.join(" 至 ")}`)}if(console.log(`初始化行程计划组件，城市: ${be}, 日期: ${xe.join(" 至 ")}`),l.value=void 0,a.value=void 0,H.value&&typeof H.value.getSelectedTrains=="function"){const w=H.value.getSelectedTrains();w&&(w.outbound&&(l.value={trainNumber:String(w.outbound.trainNumber||""),departureTime:String(w.outbound.departureTime||""),arrivalTime:String(w.outbound.arrivalTime||"")},console.log(`获取到去程火车信息: ${l.value.trainNumber}`)),w.return&&(a.value={trainNumber:String(w.return.trainNumber||""),departureTime:String(w.return.departureTime||""),arrivalTime:String(w.return.arrivalTime||"")},console.log(`获取到返程火车信息: ${a.value.trainNumber}`)))}Ie(be,xe,l.value,a.value),P(!0),console.log("行程计划页面：启用滚动")}else console.log("刷新其他页面"),P(!0),console.log("其他页面：启用滚动");setTimeout(()=>{c()},800)},_e=Q=>{if(Q>y.value&&Q!==y.value+1){console.warn(`无法跳转到步骤 ${Q}，因为当前最大步骤是 ${y.value}`);return}let te="";Q===0?te="正在切换到目的地选择...":Q===1?te="正在切换到时间选择...":Q===2&&(te="正在切换到行程计划..."),u(te),v(Q),Q===0||Q===2?(P(!0),console.log(`步骤 ${Q}: 启用滚动`)):Q===1&&(P(!1),console.log(`步骤 ${Q}: 禁用滚动`)),Q>y.value?(y.value=Q,setTimeout(()=>{ke()},100)):Q===2&&S.value?(console.log("检测到目的地或日期有变化，强制刷新行程计划页面"),S.value=!1,setTimeout(()=>{ke()},100)):setTimeout(()=>{c()},300)};return(Q,te)=>(Z(),ne(Se,null,[g("h1",{class:"main-title",ref_key:"mainTitleRef",ref:C},"你的穷游旅行规划",512),g("div",{class:"origin-input-container",ref_key:"originInputContainerRef",ref:D},[g("div",jm,[g("button",{onClick:te[0]||(te[0]=(...be)=>B(M)&&B(M)(...be))},"↺"),kt(g("input",{type:"input",style:{"text-align":"center"},"onUpdate:modelValue":te[1]||(te[1]=be=>i.value=be),placeholder:"请输入出发城市",onKeyup:sf(se,["enter"])},null,544),[[On,i.value]]),g("button",{onClick:se},"→")])],512),g("div",{class:"inbox-container",ref_key:"inboxContainerRef",ref:O},[g("div",Fm,[g("div",Hm,[g("div",{class:je({step:!0,active:B(p)[0].active,"in-progress":B(p)[0].inProgress,clickable:!0}),onClick:te[2]||(te[2]=be=>_e(0))},te[6]||(te[6]=[g("div",{class:"step-number"},"1",-1),g("div",{class:"step-text"},"选择目的地",-1)]),2),te[9]||(te[9]=g("div",{class:"step-divider"},null,-1)),g("div",{class:je({step:!0,active:B(p)[1].active,"in-progress":B(p)[1].inProgress,clickable:y.value>=1}),onClick:te[3]||(te[3]=be=>y.value>=1&&_e(1))},te[7]||(te[7]=[g("div",{class:"step-number"},"2",-1),g("div",{class:"step-text"},"选择时间",-1)]),2),te[10]||(te[10]=g("div",{class:"step-divider"},null,-1)),g("div",{class:je({step:!0,active:B(p)[2].active,"in-progress":B(p)[2].inProgress,clickable:y.value>=2}),onClick:te[4]||(te[4]=be=>y.value>=2&&_e(2))},te[8]||(te[8]=[g("div",{class:"step-number"},"3",-1),g("div",{class:"step-text"},"计划安排",-1)]),2)]),g("div",Bm,[g("button",{class:"refresh-btn refresh-data-btn",onClick:ke}," 刷新 "),g("button",{class:"refresh-btn close-btn",onClick:te[5]||(te[5]=(...be)=>B(M)&&B(M)(...be))},"关闭")])]),g("div",Um,[r.value?(Z(),ne("div",Vm,[te[11]||(te[11]=g("div",{class:"loading-spinner"},[g("div",{class:"spinner-circle"}),g("div",{class:"spinner-circle-inner"})],-1)),g("div",qm,Y(o.value),1)])):Rt("",!0),kt(Re(gh,{ref_key:"destinationSelectionRef",ref:U,onSelectDestination:st,useApi:!0,position:i.value,class:je({"content-page":!0,active:B(d)===0&&!r.value})},null,8,["position","class"]),[[sr,B(d)===0&&!r.value]]),kt(Re(rm,{ref_key:"timeSelectionRef",ref:H,onDatesSelected:at,cityName:B(fe).selectedDestination.value||"",departureCity:i.value,class:je({"content-page":!0,active:B(d)===1&&!r.value})},null,8,["cityName","departureCity","class"]),[[sr,B(d)===1&&!r.value]]),kt(Re(Dm,{class:je({"content-page":!0,active:B(d)===2&&!r.value}),ref_key:"itineraryPlanRef",ref:oe,cityName:B(fe).selectedDestination.value||"",selectedDates:B(fe).selectedDates.value,departureTrainInfo:l.value,returnTrainInfo:a.value},null,8,["class","cityName","selectedDates","departureTrainInfo","returnTrainInfo"]),[[sr,B(d)===2&&!r.value]])])],512)],64))}}),Km={class:"settings-container"},Wm={class:"settings-content"},Gm={key:0,class:"loading-overlay"},Jm={key:1,class:"settings-form"},Ym={class:"settings-section"},Qm={class:"form-group"},Xm={class:"input-container"},Zm={class:"toggle-button"},eg={class:"toggle-icon"},tg={key:0,class:"advanced-settings"},ng={class:"form-group"},sg={class:"input-container"},rg={class:"form-group"},og={class:"input-container"},ig={class:"form-group"},lg={class:"input-container"},ag={class:"settings-actions"},cg=["disabled"],ug=["disabled"],fg=At({__name:"SettingsView",setup(e){const t=z(""),n=z("qwen/qwen3-235b-a22b"),s=z("google/gemini-2.5-flash-preview"),r=z("google/gemini-2.5-flash-preview"),o=z(!1),i=z(!1),l=z(!1),a=()=>{i.value=!0;try{const f=localStorage.getItem("openrouter_api_key")||"",d=localStorage.getItem("openrouter_city_model")||"",p=localStorage.getItem("openrouter_travel_guide_model")||"",v=localStorage.getItem("openrouter_default_model")||"";t.value=f,n.value=d,s.value=p,r.value=v}catch(f){console.error("加载设置时出错:",f),gn("加载设置失败","设置错误")}finally{i.value=!1}},u=()=>{l.value=!0;try{localStorage.setItem("openrouter_api_key",t.value),localStorage.setItem("openrouter_city_model",n.value),localStorage.setItem("openrouter_travel_guide_model",s.value),localStorage.setItem("openrouter_default_model",r.value),Pr("设置已保存","保存成功")}catch(f){console.error("保存设置时出错:",f),gn("保存设置失败","设置错误")}finally{l.value=!1}},c=()=>{confirm("确定要重置所有设置吗？这将清除所有已保存的API密钥和模型设置。")&&(t.value="",n.value="",s.value="",r.value="",localStorage.removeItem("openrouter_api_key"),localStorage.removeItem("openrouter_city_model"),localStorage.removeItem("openrouter_travel_guide_model"),localStorage.removeItem("openrouter_default_model"),Pr("设置已重置","重置成功"))};return on(()=>{a()}),(f,d)=>(Z(),ne("div",Km,[d[16]||(d[16]=g("div",{class:"settings-header"},[g("h1",{class:"settings-title"},"设置"),g("p",{class:"settings-subtitle"},"配置应用程序的API密钥和模型设置")],-1)),g("div",Wm,[i.value?(Z(),ne("div",Gm,d[5]||(d[5]=[g("div",{class:"loading-spinner"},[g("div",{class:"spinner-circle"}),g("div",{class:"spinner-circle-inner"})],-1),g("div",{class:"loading-text"},"加载设置中...",-1)]))):(Z(),ne("div",Jm,[g("div",Ym,[d[15]||(d[15]=g("h2",{class:"section-title"},"OpenRouter API 设置",-1)),g("div",Qm,[d[6]||(d[6]=g("label",{for:"openrouter-key"},"API Key",-1)),g("div",Xm,[kt(g("input",{id:"openrouter-key",type:"password","onUpdate:modelValue":d[0]||(d[0]=p=>t.value=p),placeholder:"输入您的 OpenRouter API Key"},null,512),[[On,t.value]])]),d[7]||(d[7]=g("p",{class:"help-text"},[Rr(" 用于访问 OpenRouter API 的密钥。您可以在 "),g("a",{href:"https://openrouter.ai/keys",target:"_blank",rel:"noopener noreferrer"},"OpenRouter 网站"),Rr(" 上获取您的 API Key。 ")],-1))]),g("div",{class:"advanced-settings-toggle",onClick:d[1]||(d[1]=p=>o.value=!o.value)},[g("div",Zm,[d[8]||(d[8]=g("span",null,"高级设置",-1)),g("span",eg,Y(o.value?"▼":"▶"),1)])]),o.value?(Z(),ne("div",tg,[g("div",ng,[d[9]||(d[9]=g("label",{for:"default-model"},"默认模型",-1)),g("div",sg,[kt(g("input",{id:"default-model",type:"text","onUpdate:modelValue":d[2]||(d[2]=p=>r.value=p),placeholder:"例如: google/gemini-2.5-flash-preview"},null,512),[[On,r.value]])]),d[10]||(d[10]=g("p",{class:"help-text"},"用于大多数 API 调用的默认模型",-1))]),g("div",rg,[d[11]||(d[11]=g("label",{for:"city-model"},"城市推荐模型",-1)),g("div",og,[kt(g("input",{id:"city-model",type:"text","onUpdate:modelValue":d[3]||(d[3]=p=>n.value=p),placeholder:"例如: google/gemini-2.5-flash-preview"},null,512),[[On,n.value]])]),d[12]||(d[12]=g("p",{class:"help-text"},"用于城市推荐 API 的模型",-1))]),g("div",ig,[d[13]||(d[13]=g("label",{for:"travel-guide-model"},"旅游指南模型",-1)),g("div",lg,[kt(g("input",{id:"travel-guide-model",type:"text","onUpdate:modelValue":d[4]||(d[4]=p=>s.value=p),placeholder:"例如: google/gemini-2.5-flash-preview"},null,512),[[On,s.value]])]),d[14]||(d[14]=g("p",{class:"help-text"},"用于旅游指南生成 API 的模型",-1))])])):Rt("",!0)]),g("div",ag,[g("button",{class:"save-btn",onClick:u,disabled:l.value},Y(l.value?"保存中...":"保存设置"),9,cg),g("button",{class:"reset-btn",onClick:c,disabled:l.value}," 重置设置 ",8,ug)])]))])]))}}),dg=Kl(fg,[["__scopeId","data-v-44653cef"]]),pg=bd({history:Gf("/"),routes:[{path:"/",name:"home",component:zm},{path:"/settings",name:"settings",component:dg}]}),ns=lf(Td);ns.component("NotificationPopup",Or);ns.config.globalProperties.$notification={state:Me,close:Wl};ns.use(ff());ns.use(pg);ns.mount("#app");
